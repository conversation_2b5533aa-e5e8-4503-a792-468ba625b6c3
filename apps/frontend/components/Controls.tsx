/**
 * 矩阵控制组件 - 集成版本
 * 🎯 核心价值：集成控制面板，包含矩阵控制、词库管理、版本管理
 * 📦 功能范围：模式切换、配置调整、状态显示、词库管理、版本管理
 * 🔄 架构设计：标签页切换的多功能控制面板
 */

'use client';

import Button from '@/components/ui/Button';
import CascadeSelect from '@/components/ui/CascadeSelect';
import { RefreshIcon } from '@/components/ui/Icons';
import { useMatrixStore } from '@/core/matrix/MatrixStore';
import type { BusinessMode, ContentMode, MainMode } from '@/core/matrix/MatrixTypes';
import WordLibraryPanel from './WordLibraryPanel';
import VersionManagementPanel from './VersionManagementPanel';
import React, { memo, useCallback, useState } from 'react';

// ===== 标签页类型 =====

type TabType = 'matrix' | 'wordLibrary' | 'version';

// ===== 组件属性 =====

interface ControlsProps {
  /** 自定义样式 */
  className?: string;
  style?: React.CSSProperties;

  /** 显示配置 */
  showModeSelector?: boolean;
  showStatusBar?: boolean;

  /** 事件回调 */
  onModeChange?: (mode: BusinessMode) => void;
  onModeConfigChange?: (mainMode: MainMode, contentMode: ContentMode) => void;
  onReset?: () => void;
}

// ===== 统一的模式配置 =====

const MODE_LABELS: Record<BusinessMode, string> = {
  coordinate: '坐标',
  color: '颜色',
  level: '等级',
  word: '词语',
};

const TAB_LABELS: Record<TabType, string> = {
  matrix: '矩阵控制',
  wordLibrary: '词库管理',
  version: '版本管理',
};

// ===== 主控制组件 =====

const ControlsComponent: React.FC<ControlsProps> = ({
  className = '',
  style,
  showModeSelector = true,
  showStatusBar = true,
  onModeChange,
  onModeConfigChange,
  onReset,
}) => {
  const {
    data,
    config,
    setModeConfig,
    getDataAvailability,
    initializeMatrix
  } = useMatrixStore();

  const [activeTab, setActiveTab] = useState<TabType>('matrix');
  const [isVersionPanelVisible, setIsVersionPanelVisible] = useState(false);

  const mode = config.mode;
  const mainMode = config.mainMode || 'default';
  const contentMode = config.contentMode || 'blank';
  const selectedCells = data.selectedCells;

  // 获取数据可用性
  const dataAvailability = getDataAvailability();

  // 处理新模式配置切换
  const handleModeConfigChange = useCallback((newMainMode: MainMode, newContentMode: ContentMode) => {
    setModeConfig(newMainMode, newContentMode);
    onModeConfigChange?.(newMainMode, newContentMode);
  }, [setModeConfig, onModeConfigChange]);

  // 处理重置
  const handleReset = useCallback(() => {
    // 重置到默认模式下的空白模式
    setModeConfig('default', 'blank');
    initializeMatrix();
    onReset?.();
  }, [setModeConfig, initializeMatrix, onReset]);

  // 处理标签页切换
  const handleTabChange = useCallback((tab: TabType) => {
    setActiveTab(tab);
    if (tab === 'version') {
      setIsVersionPanelVisible(true);
    } else {
      setIsVersionPanelVisible(false);
    }
  }, []);

  // 渲染标签页导航
  const renderTabNavigation = () => (
    <div className="flex border-b border-gray-200 bg-gray-50">
      {(Object.keys(TAB_LABELS) as TabType[]).map((tab) => (
        <button
          key={tab}
          onClick={() => handleTabChange(tab)}
          className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
            activeTab === tab
              ? 'bg-white border-b-2 border-blue-500 text-blue-600'
              : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
          }`}
        >
          {TAB_LABELS[tab]}
        </button>
      ))}
    </div>
  );

  // 渲染矩阵控制面板
  const renderMatrixControls = () => (
    <div className="p-4 space-y-6">
      {/* 模式选择器 */}
      {showModeSelector && (
        <div className="mode-selector">
          <CascadeSelect
            mainMode={mainMode}
            contentMode={contentMode}
            onModeChange={handleModeConfigChange}
            dataAvailability={dataAvailability}
          />
        </div>
      )}

      {/* 操作按钮 */}
      <div className="space-y-2">
        <Button
          variant="danger"
          onClick={handleReset}
          className="w-full"
        >
          <RefreshIcon size={16} className="mr-2" />
          重置矩阵
        </Button>
      </div>
    </div>
  );

  // 渲染词库管理面板
  const renderWordLibraryPanel = () => (
    <div className="h-full overflow-hidden">
      <WordLibraryPanel />
    </div>
  );

  // 渲染版本管理面板
  const renderVersionPanel = () => (
    <div className="h-full">
      <VersionManagementPanel
        isVisible={true}
        onClose={() => handleTabChange('matrix')}
      />
    </div>
  );

  // 渲染当前标签页内容
  const renderTabContent = () => {
    switch (activeTab) {
      case 'matrix':
        return renderMatrixControls();
      case 'wordLibrary':
        return renderWordLibraryPanel();
      case 'version':
        return renderVersionPanel();
      default:
        return renderMatrixControls();
    }
  };

  return (
    <div className={`controls-container ${className} flex flex-col h-full`} style={style}>
      {/* 标签页导航 */}
      {renderTabNavigation()}

      {/* 标签页内容 */}
      <div className="flex-1 overflow-hidden">
        {renderTabContent()}
      </div>

      {/* 状态栏 */}
      {showStatusBar && activeTab === 'matrix' && (
        <div className="status-bar p-3 bg-gray-50 border-t text-sm text-gray-600">
          <div className="flex items-center space-x-4">
            <span>模式: {MODE_LABELS[mode]}</span>
            <span>已选择: {selectedCells.size}</span>
            <span>总计: 1089</span>
          </div>
        </div>
      )}
    </div>
  );
};

// ===== 性能优化 =====

const Controls = memo(ControlsComponent);

Controls.displayName = 'Controls';

export default Controls;
