/**
 * 词语表单组件
 * 🎯 核心价值：提供词语添加和编辑的表单界面，支持实时验证和错误提示
 * 📦 功能范围：表单输入、实时验证、错误显示、提交处理
 * 🔄 架构设计：基于受控组件的表单管理，支持异步验证和提交
 */

'use client';

import React, { useCallback, useEffect, useState } from 'react';
import type { BasicColorType } from '../core/matrix/MatrixTypes';
import { useWordLibrary, useWordValidation } from '../core/word-library/useWordLibrary';
import type { ColorLevel, CreateWordParams, UpdateWordParams, WordEntry } from '../core/word-library/WordLibraryTypes';

// ===== 类型定义 =====

interface WordFormProps {
  mode: 'add' | 'edit';
  word?: WordEntry;
  initialCategory?: { color: BasicColorType; level: ColorLevel };
  onSubmit?: (success: boolean, wordId?: string) => void;
  onCancel?: () => void;
  className?: string;
}

interface FormData {
  text: string;
  color: BasicColorType;
  level: ColorLevel;
}

// ===== 配置常量 =====

const COLOR_OPTIONS: Array<{ value: BasicColorType; label: string; hex: string }> = [
  { value: 'red', label: '红色', hex: '#ef4444' },
  { value: 'cyan', label: '青色', hex: '#06b6d4' },
  { value: 'yellow', label: '黄色', hex: '#eab308' },
  { value: 'purple', label: '紫色', hex: '#a855f7' },
  { value: 'orange', label: '橙色', hex: '#f97316' },
  { value: 'green', label: '绿色', hex: '#22c55e' },
  { value: 'blue', label: '蓝色', hex: '#3b82f6' },
  { value: 'pink', label: '粉色', hex: '#ec4899' },
  { value: 'black', label: '黑色', hex: '#374151' },
];

const LEVEL_OPTIONS: Array<{ value: ColorLevel; label: string }> = [
  { value: 1, label: '等级1' },
  { value: 2, label: '等级2' },
  { value: 3, label: '等级3' },
  { value: 4, label: '等级4' },
];

// ===== 主组件 =====

export const WordForm: React.FC<WordFormProps> = ({
  mode,
  word,
  initialCategory,
  onSubmit,
  onCancel,
  className = '',
}) => {
  const { addWord, updateWord } = useWordLibrary();
  const { validate, clearValidation, validationResult, isValid, errors } = useWordValidation();

  // 表单状态
  const [formData, setFormData] = useState<FormData>(() => ({
    text: word?.text || '',
    color: word?.category.color || initialCategory?.color || 'red',
    level: word?.category.level || initialCategory?.level || 1,
  }));

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasValidated, setHasValidated] = useState(false);

  // 实时验证
  useEffect(() => {
    if (formData.text && hasValidated) {
      validate(formData.text, mode === 'edit' ? word?.id : undefined);
    } else if (!formData.text) {
      clearValidation();
    }
  }, [formData.text, hasValidated, validate, clearValidation, mode, word?.id]);

  // 处理输入变化
  const handleInputChange = useCallback((field: keyof FormData, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    if (field === 'text' && !hasValidated) {
      setHasValidated(true);
    }
  }, [hasValidated]);

  // 处理表单提交
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.text.trim()) {
      return;
    }

    // 最终验证
    const finalValidation = validate(formData.text, mode === 'edit' ? word?.id : undefined);
    if (!finalValidation.isValid) {
      return;
    }

    setIsSubmitting(true);

    try {
      if (mode === 'add') {
        const params: CreateWordParams = {
          text: formData.text.trim(),
          category: {
            color: formData.color,
            level: formData.level,
          },
          validation: { isValid: true }, // 添加验证信息
        };

        const result = await addWord(params);
        if (result.success) {
          onSubmit?.(true, result.wordId);
          // 重置表单
          setFormData({
            text: '',
            color: initialCategory?.color || 'red',
            level: initialCategory?.level || 1,
          });
          clearValidation();
          setHasValidated(false);
        } else {
          alert(`添加失败: ${result.error}`);
        }
      } else if (mode === 'edit' && word) {
        const updates: UpdateWordParams = {
          text: formData.text.trim(),
          category: {
            color: formData.color,
            level: formData.level,
          },
        };

        const result = await updateWord(word.id, updates);
        if (result.success) {
          onSubmit?.(true, word.id);
        } else {
          alert(`更新失败: ${result.error}`);
        }
      }
    } catch (error) {
      console.error('提交失败:', error);
      alert('操作失败，请重试');
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, mode, word, validate, addWord, updateWord, onSubmit, initialCategory, clearValidation]);

  // 处理取消
  const handleCancel = useCallback(() => {
    if (mode === 'add') {
      setFormData({
        text: '',
        color: initialCategory?.color || 'red',
        level: initialCategory?.level || 1,
      });
      clearValidation();
      setHasValidated(false);
    }
    onCancel?.();
  }, [mode, initialCategory, clearValidation, onCancel]);

  const canSubmit = formData.text.trim() && (isValid === null || isValid === true) && !isSubmitting;

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      <div className="px-4 py-3 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">
          {mode === 'add' ? '添加词语' : '编辑词语'}
        </h3>
      </div>

      <form onSubmit={handleSubmit} className="p-4 space-y-4">
        {/* 词语文本输入 */}
        <div>
          <label htmlFor="word-text" className="block text-sm font-medium text-gray-700 mb-2">
            词语文本 *
          </label>
          <input
            id="word-text"
            type="text"
            value={formData.text}
            onChange={(e) => handleInputChange('text', e.target.value)}
            placeholder="请输入中文词语"
            className={`
              w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent
              ${errors.length > 0 ? 'border-red-300' : 'border-gray-300'}
            `}
            maxLength={10}
            disabled={isSubmitting}
          />

          {/* 字符计数 */}
          <div className="mt-1 text-xs text-gray-500 text-right">
            {formData.text.length}/10
          </div>

          {/* 验证错误显示 */}
          {errors.length > 0 && (
            <div className="mt-2 space-y-1">
              {errors.map((error, index) => (
                <div key={index} className="text-sm text-red-600 flex items-center gap-1">
                  <span>⚠️</span>
                  {error}
                </div>
              ))}
            </div>
          )}

          {/* 验证成功提示 */}
          {isValid === true && formData.text && (
            <div className="mt-2 text-sm text-green-600 flex items-center gap-1">
              <span>✅</span>
              词语验证通过
            </div>
          )}
        </div>

        {/* 颜色选择 */}
        <div>
          <label htmlFor="word-color" className="block text-sm font-medium text-gray-700 mb-2">
            颜色分类 *
          </label>
          <div className="grid grid-cols-3 gap-2">
            {COLOR_OPTIONS.map((option) => (
              <button
                key={option.value}
                type="button"
                onClick={() => handleInputChange('color', option.value)}
                className={`
                  flex items-center gap-2 px-3 py-2 rounded-lg border transition-all duration-200
                  ${formData.color === option.value
                    ? 'bg-blue-50 border-blue-300 text-blue-700'
                    : 'bg-white border-gray-200 text-gray-600 hover:bg-gray-50'
                  }
                `}
                disabled={isSubmitting}
              >
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: option.hex }}
                />
                <span className="text-sm">{option.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* 等级选择 */}
        <div>
          <label htmlFor="word-level" className="block text-sm font-medium text-gray-700 mb-2">
            等级分类 *
          </label>
          <div className="flex gap-2">
            {LEVEL_OPTIONS.map((option) => (
              <button
                key={option.value}
                type="button"
                onClick={() => handleInputChange('level', option.value)}
                className={`
                  px-4 py-2 rounded-lg border transition-all duration-200
                  ${formData.level === option.value
                    ? 'bg-blue-50 border-blue-300 text-blue-700'
                    : 'bg-white border-gray-200 text-gray-600 hover:bg-gray-50'
                  }
                `}
                disabled={isSubmitting}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex gap-3 pt-4 border-t border-gray-200">
          <button
            type="submit"
            disabled={!canSubmit}
            className={`
              flex-1 py-2 px-4 rounded-lg font-medium transition-all duration-200
              ${canSubmit
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }
            `}
          >
            {isSubmitting ? '处理中...' : (mode === 'add' ? '添加词语' : '保存更改')}
          </button>

          <button
            type="button"
            onClick={handleCancel}
            disabled={isSubmitting}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-all duration-200"
          >
            取消
          </button>
        </div>
      </form>
    </div>
  );
};

export default WordForm;
