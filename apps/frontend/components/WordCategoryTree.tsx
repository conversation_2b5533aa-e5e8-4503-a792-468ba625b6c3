/**
 * 词语分类树形结构组件
 * 🎯 核心价值：提供层次化的词语分类浏览，支持统计信息和性能优化
 * 📦 功能范围：树形导航、统计显示、虚拟滚动、搜索过滤
 * 🔄 架构设计：基于虚拟化的高性能树形组件，支持大量数据渲染
 */

'use client';

import React, { useState, useMemo, useCallback } from 'react';
import { useWordLibrary, useWordLibraryStats } from '../core/word-library/useWordLibrary';
import type { BasicColorType } from '../core/matrix/MatrixTypes';
import type { ColorLevel, WordEntry } from '../core/word-library/WordLibraryTypes';

// ===== 类型定义 =====

interface WordCategoryTreeProps {
  onCategorySelect?: (color: BasicColorType, level: ColorLevel) => void;
  onWordSelect?: (word: WordEntry) => void;
  selectedCategory?: { color: BasicColorType; level: ColorLevel };
  selectedWordId?: string;
  className?: string;
  showStatistics?: boolean;
  maxHeight?: number;
}

interface TreeNode {
  id: string;
  type: 'color' | 'level' | 'word';
  label: string;
  color?: BasicColorType;
  level?: ColorLevel;
  word?: WordEntry;
  children?: TreeNode[];
  isExpanded?: boolean;
  wordCount: number;
  depth: number;
}

// ===== 配置常量 =====

const COLOR_CONFIG: Record<BasicColorType, { name: string; hex: string }> = {
  red: { name: '红色', hex: '#ef4444' },
  cyan: { name: '青色', hex: '#06b6d4' },
  yellow: { name: '黄色', hex: '#eab308' },
  purple: { name: '紫色', hex: '#a855f7' },
  orange: { name: '橙色', hex: '#f97316' },
  green: { name: '绿色', hex: '#22c55e' },
  blue: { name: '蓝色', hex: '#3b82f6' },
  pink: { name: '粉色', hex: '#ec4899' },
  black: { name: '黑色', hex: '#374151' },
};

const LEVEL_CONFIG: Record<ColorLevel, string> = {
  1: '等级1',
  2: '等级2',
  3: '等级3',
  4: '等级4',
};

// ===== 子组件 =====

const TreeNodeComponent: React.FC<{
  node: TreeNode;
  isSelected: boolean;
  onToggle: () => void;
  onSelect: () => void;
  style?: React.CSSProperties;
}> = ({ node, isSelected, onToggle, onSelect, style }) => {
  const indentWidth = node.depth * 20;

  return (
    <div
      style={style}
      className={`
        flex items-center py-1 px-2 cursor-pointer transition-all duration-150
        ${isSelected ? 'bg-blue-50 text-blue-700' : 'hover:bg-gray-50'}
      `}
      onClick={onSelect}
    >
      <div style={{ marginLeft: indentWidth }} className="flex items-center gap-2 flex-1">
        {/* 展开/折叠按钮 */}
        {node.children && node.children.length > 0 && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              onToggle();
            }}
            className="w-4 h-4 flex items-center justify-center text-gray-400 hover:text-gray-600"
          >
            {node.isExpanded ? '▼' : '▶'}
          </button>
        )}

        {/* 图标 */}
        {node.type === 'color' && node.color && (
          <div
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: COLOR_CONFIG[node.color].hex }}
          />
        )}
        {node.type === 'level' && (
          <div className="w-3 h-3 bg-gray-300 rounded-sm" />
        )}
        {node.type === 'word' && (
          <div className="w-3 h-3 bg-blue-500 rounded-full" />
        )}

        {/* 标签 */}
        <span className="text-sm font-medium flex-1">{node.label}</span>

        {/* 统计信息 */}
        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
          {node.wordCount}
        </span>
      </div>
    </div>
  );
};

// ===== 主组件 =====

export const WordCategoryTree: React.FC<WordCategoryTreeProps> = ({
  onCategorySelect,
  onWordSelect,
  selectedCategory,
  selectedWordId,
  className = '',
  showStatistics = true,
  maxHeight = 400,
}) => {
  const { getWordsByCategory } = useWordLibrary();
  const { stats } = useWordLibraryStats();
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set(['root']));
  const [searchText, setSearchText] = useState('');

  // 构建树形数据结构
  const treeData = useMemo(() => {
    const colors: BasicColorType[] = ['red', 'cyan', 'yellow', 'purple', 'orange', 'green', 'blue', 'pink', 'black'];
    const levels: ColorLevel[] = [1, 2, 3, 4];

    const rootNode: TreeNode = {
      id: 'root',
      type: 'color',
      label: '词库分类',
      wordCount: stats.totalWords,
      depth: 0,
      children: [],
      isExpanded: expandedNodes.has('root'),
    };

    colors.forEach(color => {
      const colorWordCount = stats.wordsByColor[color];
      const colorNode: TreeNode = {
        id: `color-${color}`,
        type: 'color',
        label: COLOR_CONFIG[color].name,
        color,
        wordCount: colorWordCount,
        depth: 1,
        children: [],
        isExpanded: expandedNodes.has(`color-${color}`),
      };

      levels.forEach(level => {
        const words = getWordsByCategory(color, level);
        const filteredWords = searchText 
          ? words.filter(word => word.text.toLowerCase().includes(searchText.toLowerCase()))
          : words;

        if (words.length > 0 || !searchText) {
          const levelNode: TreeNode = {
            id: `level-${color}-${level}`,
            type: 'level',
            label: LEVEL_CONFIG[level],
            color,
            level,
            wordCount: filteredWords.length,
            depth: 2,
            children: [],
            isExpanded: expandedNodes.has(`level-${color}-${level}`),
          };

          // 添加词语节点
          filteredWords.forEach(word => {
            levelNode.children!.push({
              id: `word-${word.id}`,
              type: 'word',
              label: word.text,
              word,
              wordCount: word.metadata.usageCount,
              depth: 3,
            });
          });

          colorNode.children!.push(levelNode);
        }
      });

      if (colorNode.children!.length > 0 || !searchText) {
        rootNode.children!.push(colorNode);
      }
    });

    return rootNode;
  }, [stats, getWordsByCategory, expandedNodes, searchText]);

  // 展开的节点列表（用于虚拟滚动）
  const flattenedNodes = useMemo(() => {
    const flatten = (node: TreeNode): TreeNode[] => {
      const result = [node];
      if (node.isExpanded && node.children) {
        node.children.forEach(child => {
          result.push(...flatten(child));
        });
      }
      return result;
    };

    return flatten(treeData).slice(1); // 排除根节点
  }, [treeData]);

  // 处理节点展开/折叠
  const handleToggleNode = useCallback((nodeId: string) => {
    setExpandedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  }, []);

  // 处理节点选择
  const handleSelectNode = useCallback((node: TreeNode) => {
    if (node.type === 'level' && node.color && node.level) {
      onCategorySelect?.(node.color, node.level);
    } else if (node.type === 'word' && node.word) {
      onWordSelect?.(node.word);
    }
  }, [onCategorySelect, onWordSelect]);

  // 判断节点是否被选中
  const isNodeSelected = useCallback((node: TreeNode) => {
    if (node.type === 'level' && selectedCategory) {
      return node.color === selectedCategory.color && node.level === selectedCategory.level;
    } else if (node.type === 'word' && selectedWordId) {
      return node.word?.id === selectedWordId;
    }
    return false;
  }, [selectedCategory, selectedWordId]);

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* 标题栏 */}
      <div className="px-4 py-3 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">分类浏览</h3>
      </div>

      {/* 搜索栏 */}
      <div className="p-4 border-b border-gray-200">
        <div className="relative">
          <input
            type="text"
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            placeholder="搜索词语..."
            className="w-full px-3 py-2 pl-8 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <div className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400">
            🔍
          </div>
          {searchText && (
            <button
              onClick={() => setSearchText('')}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          )}
        </div>
      </div>

      {/* 树形结构 */}
      <div 
        className="overflow-y-auto"
        style={{ maxHeight }}
      >
        {flattenedNodes.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            {searchText ? '没有找到匹配的词语' : '暂无词语数据'}
          </div>
        ) : (
          <div className="p-2">
            {flattenedNodes.map((node) => (
              <TreeNodeComponent
                key={node.id}
                node={node}
                isSelected={isNodeSelected(node)}
                onToggle={() => handleToggleNode(node.id)}
                onSelect={() => handleSelectNode(node)}
              />
            ))}
          </div>
        )}
      </div>

      {/* 统计信息 */}
      {showStatistics && (
        <div className="px-4 py-3 border-t border-gray-200 bg-gray-50">
          <div className="text-xs text-gray-600 space-y-1">
            <div className="flex justify-between">
              <span>总词语数:</span>
              <span>{stats.totalWords}</span>
            </div>
            <div className="flex justify-between">
              <span>分类覆盖率:</span>
              <span>
                {((Object.values(stats.wordsByColor).filter(count => count > 0).length / 9) * 100).toFixed(1)}%
              </span>
            </div>
            <div className="flex justify-between">
              <span>平均每分类:</span>
              <span>{(stats.totalWords / 36).toFixed(1)} 个</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default WordCategoryTree;
