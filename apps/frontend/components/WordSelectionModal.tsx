/**
 * 词语选择模态框组件
 * 🎯 核心价值：提供双击激活的词语选择界面，支持快速填词操作
 * 📦 功能范围：词语列表显示、分类过滤、搜索功能、键盘导航
 * 🔄 架构设计：模态框形式的词语选择器，支持位置定位和响应式布局
 */

'use client';

import React, { useCallback, useEffect, useRef, useState } from 'react';
import type { BasicColorType, Coordinate } from '../core/matrix/MatrixTypes';
import { useWordLibrary } from '../core/word-library/useWordLibrary';
import type { ColorLevel, WordEntry } from '../core/word-library/WordLibraryTypes';

// ===== 类型定义 =====

interface WordSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onWordSelect: (word: WordEntry) => void;
  onWordPreview?: (word: WordEntry) => void;
  onPreviewCancel?: () => void;
  position: { x: number; y: number };
  cellCoordinate: Coordinate;
  initialCategory?: { color: BasicColorType; level: ColorLevel };
  enablePreview?: boolean;
  className?: string;
}

interface CategoryFilter {
  color: BasicColorType | 'all';
  level: ColorLevel | 'all';
}

// ===== 配置常量 =====

const COLOR_OPTIONS: Array<{ value: BasicColorType | 'all'; label: string; hex?: string }> = [
  { value: 'all', label: '全部' },
  { value: 'red', label: '红色', hex: '#ef4444' },
  { value: 'cyan', label: '青色', hex: '#06b6d4' },
  { value: 'yellow', label: '黄色', hex: '#eab308' },
  { value: 'purple', label: '紫色', hex: '#a855f7' },
  { value: 'orange', label: '橙色', hex: '#f97316' },
  { value: 'green', label: '绿色', hex: '#22c55e' },
  { value: 'blue', label: '蓝色', hex: '#3b82f6' },
  { value: 'pink', label: '粉色', hex: '#ec4899' },
  { value: 'black', label: '黑色', hex: '#374151' },
];

const LEVEL_OPTIONS: Array<{ value: ColorLevel | 'all'; label: string }> = [
  { value: 'all', label: '全部' },
  { value: 1, label: '等级1' },
  { value: 2, label: '等级2' },
  { value: 3, label: '等级3' },
  { value: 4, label: '等级4' },
];

// ===== 主组件 =====

export const WordSelectionModal: React.FC<WordSelectionModalProps> = ({
  isOpen,
  onClose,
  onWordSelect,
  onWordPreview,
  onPreviewCancel,
  position,
  cellCoordinate,
  initialCategory,
  enablePreview = true,
  className = '',
}) => {
  const { getWordsByCategory, getAllWords } = useWordLibrary();
  const modalRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // 状态管理
  const [categoryFilter, setCategoryFilter] = useState<CategoryFilter>({
    color: initialCategory?.color || 'all',
    level: initialCategory?.level || 'all',
  });

  const [searchText, setSearchText] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  // 获取过滤后的词语列表
  const filteredWords = React.useMemo(() => {
    let words: WordEntry[] = [];

    if (categoryFilter.color === 'all' && categoryFilter.level === 'all') {
      words = getAllWords();
    } else if (categoryFilter.color === 'all') {
      // 只按等级过滤
      words = getAllWords().filter(word =>
        categoryFilter.level === 'all' || word.category.level === categoryFilter.level
      );
    } else if (categoryFilter.level === 'all') {
      // 只按颜色过滤
      words = getAllWords().filter(word => word.category.color === categoryFilter.color);
    } else {
      // 按颜色和等级过滤
      words = getWordsByCategory(categoryFilter.color as BasicColorType, categoryFilter.level as ColorLevel);
    }

    // 搜索过滤
    if (searchText) {
      words = words.filter(word =>
        word.text.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    return words;
  }, [categoryFilter, searchText, getWordsByCategory, getAllWords]);

  // 处理模态框显示/隐藏动画
  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
      // 聚焦搜索框
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    } else {
      setIsVisible(false);
      setSearchText('');
      setSelectedIndex(0);
    }
  }, [isOpen]);

  // 处理分类过滤变化
  const handleCategoryChange = useCallback((type: 'color' | 'level', value: string | number) => {
    setCategoryFilter(prev => ({
      ...prev,
      [type]: value,
    }));
    setSelectedIndex(0); // 重置选择索引
  }, []);

  // 键盘导航处理
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    switch (event.key) {
      case 'ArrowDown':
      case 'j': // Vim风格导航
        event.preventDefault();
        setSelectedIndex(prev => {
          const newIndex = Math.min(prev + 1, filteredWords.length - 1);
          // 滚动到可见区域
          setTimeout(() => {
            const element = document.querySelector(`[data-word-index="${newIndex}"]`);
            element?.scrollIntoView({ block: 'nearest' });
          }, 0);
          return newIndex;
        });
        break;
      case 'ArrowUp':
      case 'k': // Vim风格导航
        event.preventDefault();
        setSelectedIndex(prev => {
          const newIndex = Math.max(prev - 1, 0);
          // 滚动到可见区域
          setTimeout(() => {
            const element = document.querySelector(`[data-word-index="${newIndex}"]`);
            element?.scrollIntoView({ block: 'nearest' });
          }, 0);
          return newIndex;
        });
        break;
      case 'PageDown':
        event.preventDefault();
        setSelectedIndex(prev => Math.min(prev + 5, filteredWords.length - 1));
        break;
      case 'PageUp':
        event.preventDefault();
        setSelectedIndex(prev => Math.max(prev - 5, 0));
        break;
      case 'Home':
        event.preventDefault();
        setSelectedIndex(0);
        break;
      case 'End':
        event.preventDefault();
        setSelectedIndex(filteredWords.length - 1);
        break;
      case 'Enter':
      case ' ': // 空格键也可以选择
        event.preventDefault();
        if (filteredWords[selectedIndex]) {
          onWordSelect(filteredWords[selectedIndex]);
        }
        break;
      case 'Escape':
        event.preventDefault();
        onClose();
        break;
      case 'Tab':
        event.preventDefault();
        // Tab键切换分类过滤
        if (event.shiftKey) {
          // Shift+Tab 切换等级
          const currentLevelIndex = LEVEL_OPTIONS.findIndex(opt => opt.value === categoryFilter.level);
          const nextLevelIndex = (currentLevelIndex - 1 + LEVEL_OPTIONS.length) % LEVEL_OPTIONS.length;
          handleCategoryChange('level', LEVEL_OPTIONS[nextLevelIndex].value);
        } else {
          // Tab 切换颜色
          const currentColorIndex = COLOR_OPTIONS.findIndex(opt => opt.value === categoryFilter.color);
          const nextColorIndex = (currentColorIndex + 1) % COLOR_OPTIONS.length;
          handleCategoryChange('color', COLOR_OPTIONS[nextColorIndex].value);
        }
        break;
      case '/':
        event.preventDefault();
        // 聚焦搜索框
        searchInputRef.current?.focus();
        break;
    }
  }, [filteredWords, selectedIndex, onWordSelect, onClose, categoryFilter, handleCategoryChange]);

  // 处理词语选择
  const handleWordClick = useCallback((word: WordEntry, index: number) => {
    setSelectedIndex(index);
    onWordSelect(word);
  }, [onWordSelect]);

  // 计算模态框位置
  const modalStyle = React.useMemo(() => {
    if (!isVisible) return { display: 'none' };

    const modalWidth = 400;
    const modalHeight = 500;
    const padding = 20;

    let left = position.x;
    let top = position.y + 40; // 在单元格下方显示

    // 防止超出右边界
    if (left + modalWidth > window.innerWidth - padding) {
      left = window.innerWidth - modalWidth - padding;
    }

    // 防止超出左边界
    if (left < padding) {
      left = padding;
    }

    // 防止超出下边界
    if (top + modalHeight > window.innerHeight - padding) {
      top = position.y - modalHeight - 10; // 在单元格上方显示
    }

    // 防止超出上边界
    if (top < padding) {
      top = padding;
    }

    return {
      position: 'fixed' as const,
      left: `${left}px`,
      top: `${top}px`,
      zIndex: 1000,
    };
  }, [position, isVisible]);

  // 点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div
      ref={modalRef}
      className={`bg-white border border-gray-200 rounded-lg shadow-xl ${className}`}
      style={modalStyle}
      onKeyDown={handleKeyDown}
      tabIndex={-1}
    >
      {/* 标题栏 */}
      <div className="px-4 py-3 border-b border-gray-200 flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">
          选择词语 ({cellCoordinate.x}, {cellCoordinate.y})
        </h3>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 text-xl"
        >
          ×
        </button>
      </div>

      {/* 搜索栏 */}
      <div className="p-4 border-b border-gray-200">
        <input
          ref={searchInputRef}
          type="text"
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          placeholder="搜索词语..."
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {/* 分类过滤 */}
      <div className="p-4 border-b border-gray-200 space-y-3">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">颜色分类</label>
          <div className="flex flex-wrap gap-1">
            {COLOR_OPTIONS.map((option) => (
              <button
                key={option.value}
                onClick={() => handleCategoryChange('color', option.value)}
                className={`
                  flex items-center gap-1 px-2 py-1 rounded text-xs transition-all duration-200
                  ${categoryFilter.color === option.value
                    ? 'bg-blue-100 text-blue-700 border border-blue-300'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }
                `}
              >
                {option.hex && (
                  <div
                    className="w-2 h-2 rounded-full"
                    style={{ backgroundColor: option.hex }}
                  />
                )}
                {option.label}
              </button>
            ))}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">等级分类</label>
          <div className="flex gap-1">
            {LEVEL_OPTIONS.map((option) => (
              <button
                key={option.value}
                onClick={() => handleCategoryChange('level', option.value)}
                className={`
                  px-2 py-1 rounded text-xs transition-all duration-200
                  ${categoryFilter.level === option.value
                    ? 'bg-blue-100 text-blue-700 border border-blue-300'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }
                `}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* 词语列表 */}
      <div className="max-h-64 overflow-y-auto">
        {filteredWords.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            {searchText ? '没有找到匹配的词语' : '该分类暂无词语'}
          </div>
        ) : (
          <div className="p-2">
            {filteredWords.map((word, index) => (
              <div
                key={word.id}
                data-word-index={index}
                onClick={() => handleWordClick(word, index)}
                className={`
                  flex items-center justify-between p-2 rounded cursor-pointer transition-all duration-150
                  ${index === selectedIndex
                    ? 'bg-blue-50 border border-blue-300 ring-2 ring-blue-200'
                    : 'hover:bg-gray-50 border border-transparent'
                  }
                `}
              >
                <div className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: COLOR_OPTIONS.find(c => c.value === word.category.color)?.hex || '#gray' }}
                  />
                  <span className="font-medium">{word.text}</span>
                  <span className="text-xs text-gray-500">
                    等级{word.category.level}
                  </span>
                </div>
                <div className="text-xs text-gray-400">
                  使用 {word.metadata.usageCount} 次
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 底部提示 */}
      <div className="px-4 py-2 border-t border-gray-200 bg-gray-50 text-xs text-gray-600">
        <div className="space-y-1">
          <div className="flex justify-between">
            <span>↑↓/jk 导航 | Enter/Space 选择 | Esc 取消</span>
            <span>共 {filteredWords.length} 个词语</span>
          </div>
          <div className="flex justify-between">
            <span>Tab 切换颜色 | Shift+Tab 切换等级 | / 搜索</span>
            <span>PgUp/PgDn 快速导航</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WordSelectionModal;
