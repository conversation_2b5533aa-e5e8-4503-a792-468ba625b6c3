/**
 * GridCell 组件样式
 * 🎯 核心价值：统一的单元格视觉效果和动画
 * 📦 功能范围：基础样式、状态样式、动画效果、词语相关样式
 * 🔄 架构设计：基于CSS模块的样式隔离，支持主题切换
 */

/* ===== 基础样式 ===== */

.gridCell {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  user-select: none;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  border-radius: 6px;
  transition: all 0.2s ease-in-out;
  font-size: 12px;
  font-weight: 400;
  line-height: 1;
}

/* ===== 状态样式 ===== */

.gridCell:hover {
  z-index: 5;
}

.gridCell:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 1px;
  z-index: 10;
}

/* ===== 词语填充相关样式 ===== */

.gridCellSelected {
  box-shadow: 0 0 0 2px #3b82f6, 0 0 8px rgba(59, 130, 246, 0.3);
  z-index: 10;
}

.gridCellHovered {
  transform: scale(1.05);
  z-index: 5;
}

.gridCellDuplicate {
  border: 2px solid #ef4444 !important;
  box-shadow: 0 0 8px rgba(239, 68, 68, 0.3);
  animation: duplicateWarning 2s infinite;
}

.gridCellPreview {
  background-color: #fef3c7 !important;
  color: #92400e !important;
  border: 2px dashed #f59e0b !important;
  box-shadow: 0 0 12px rgba(245, 158, 11, 0.4);
  animation: previewPulse 2s infinite;
}

.gridCellHasWord {
  font-weight: 600;
  border: 2px solid #10b981;
  box-shadow: 0 0 4px rgba(16, 185, 129, 0.2);
}

/* ===== 动画效果 ===== */

@keyframes duplicateWarning {
  0%, 100% {
    border-color: #ef4444;
    box-shadow: 0 0 8px rgba(239, 68, 68, 0.3);
  }
  50% {
    border-color: #dc2626;
    box-shadow: 0 0 12px rgba(239, 68, 68, 0.5);
  }
}

@keyframes previewPulse {
  0%, 100% {
    opacity: 0.9;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}

@keyframes wordFillSuccess {
  0% {
    transform: scale(1);
    box-shadow: 0 0 4px rgba(16, 185, 129, 0.2);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 12px rgba(16, 185, 129, 0.6);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 4px rgba(16, 185, 129, 0.2);
  }
}

/* ===== 主题样式 ===== */

.gridCell[data-theme="minimal"] {
  border: 1px solid #f3f4f6;
  background-color: #ffffff;
}

.gridCell[data-theme="colorful"] {
  border: 1px solid #e5e7eb;
  background-color: #ffffff;
}

/* ===== 响应式样式 ===== */

@media (max-width: 768px) {
  .gridCell {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .gridCell {
    font-size: 8px;
  }
}

/* ===== 可访问性样式 ===== */

.gridCell:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
  .gridCell,
  .gridCellSelected,
  .gridCellHovered,
  .gridCellDuplicate,
  .gridCellPreview {
    transition: none;
    animation: none;
  }
}

/* ===== 高对比度模式 ===== */

@media (prefers-contrast: high) {
  .gridCell {
    border-width: 2px;
  }
  
  .gridCellSelected {
    border: 3px solid #000000;
    box-shadow: 0 0 0 1px #ffffff, 0 0 0 4px #000000;
  }
  
  .gridCellDuplicate {
    border: 3px solid #cc0000 !important;
  }
  
  .gridCellHasWord {
    border: 3px solid #006600;
  }
}

/* ===== 打印样式 ===== */

@media print {
  .gridCell {
    transition: none;
    animation: none;
    box-shadow: none !important;
    transform: none !important;
  }
  
  .gridCellSelected,
  .gridCellHovered {
    border: 2px solid #000000;
  }
  
  .gridCellDuplicate {
    border: 2px solid #000000;
    background-color: #f0f0f0 !important;
  }
  
  .gridCellPreview {
    border: 2px dashed #000000;
    background-color: #f8f8f8 !important;
  }
}
