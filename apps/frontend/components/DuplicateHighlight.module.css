/**
 * 重复词语高亮样式
 * 🎯 核心价值：提供丰富的视觉反馈，帮助用户识别重复词语
 * 📦 功能范围：高亮效果、动画过渡、用户自定义、无障碍支持
 * 🔄 架构设计：基于CSS变量的主题系统，支持动态样式切换
 */

/* ===== CSS变量定义 ===== */
.duplicateHighlight {
  /* 默认高亮颜色 */
  --duplicate-primary: #ff6b6b;
  --duplicate-secondary: #feca57;
  --duplicate-tertiary: #48dbfb;
  --duplicate-quaternary: #ff9ff3;
  
  /* 透明度级别 */
  --duplicate-alpha-light: 0.2;
  --duplicate-alpha-medium: 0.4;
  --duplicate-alpha-strong: 0.6;
  --duplicate-alpha-intense: 0.8;
  
  /* 动画时长 */
  --duplicate-transition-fast: 0.15s;
  --duplicate-transition-normal: 0.3s;
  --duplicate-transition-slow: 0.5s;
  
  /* 边框样式 */
  --duplicate-border-width: 2px;
  --duplicate-border-radius: 4px;
  
  /* 阴影效果 */
  --duplicate-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.1);
  --duplicate-shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.15);
  --duplicate-shadow-strong: 0 6px 12px rgba(0, 0, 0, 0.2);
}

/* ===== 基础高亮样式 ===== */

/* 轻度重复（2-3次使用） */
.duplicateLight {
  background-color: rgba(255, 107, 107, var(--duplicate-alpha-light));
  border: var(--duplicate-border-width) solid rgba(255, 107, 107, var(--duplicate-alpha-medium));
  border-radius: var(--duplicate-border-radius);
  box-shadow: var(--duplicate-shadow-light);
  transition: all var(--duplicate-transition-normal) ease-in-out;
}

.duplicateLight:hover {
  background-color: rgba(255, 107, 107, var(--duplicate-alpha-medium));
  border-color: rgba(255, 107, 107, var(--duplicate-alpha-strong));
  box-shadow: var(--duplicate-shadow-medium);
  transform: translateY(-1px);
}

/* 中度重复（4-5次使用） */
.duplicateMedium {
  background-color: rgba(254, 202, 87, var(--duplicate-alpha-medium));
  border: var(--duplicate-border-width) solid rgba(254, 202, 87, var(--duplicate-alpha-strong));
  border-radius: var(--duplicate-border-radius);
  box-shadow: var(--duplicate-shadow-medium);
  transition: all var(--duplicate-transition-normal) ease-in-out;
}

.duplicateMedium:hover {
  background-color: rgba(254, 202, 87, var(--duplicate-alpha-strong));
  border-color: rgba(254, 202, 87, var(--duplicate-alpha-intense));
  box-shadow: var(--duplicate-shadow-strong);
  transform: translateY(-1px);
}

/* 重度重复（6次以上使用） */
.duplicateHeavy {
  background-color: rgba(72, 219, 251, var(--duplicate-alpha-strong));
  border: var(--duplicate-border-width) solid rgba(72, 219, 251, var(--duplicate-alpha-intense));
  border-radius: var(--duplicate-border-radius);
  box-shadow: var(--duplicate-shadow-strong);
  transition: all var(--duplicate-transition-normal) ease-in-out;
  animation: duplicatePulse 2s infinite;
}

.duplicateHeavy:hover {
  background-color: rgba(72, 219, 251, var(--duplicate-alpha-intense));
  border-color: rgba(72, 219, 251, 1);
  box-shadow: 0 8px 16px rgba(72, 219, 251, 0.3);
  transform: translateY(-2px);
  animation-play-state: paused;
}

/* 极度重复（10次以上使用） */
.duplicateExtreme {
  background-color: rgba(255, 159, 243, var(--duplicate-alpha-intense));
  border: var(--duplicate-border-width) solid rgba(255, 159, 243, 1);
  border-radius: var(--duplicate-border-radius);
  box-shadow: 0 8px 16px rgba(255, 159, 243, 0.4);
  transition: all var(--duplicate-transition-normal) ease-in-out;
  animation: duplicateIntense 1.5s infinite;
}

.duplicateExtreme:hover {
  background-color: rgba(255, 159, 243, 1);
  box-shadow: 0 12px 24px rgba(255, 159, 243, 0.5);
  transform: translateY(-3px) scale(1.02);
  animation-play-state: paused;
}

/* ===== 动画效果 ===== */

/* 轻微脉冲动画 */
@keyframes duplicatePulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.01);
  }
}

/* 强烈闪烁动画 */
@keyframes duplicateIntense {
  0%, 100% {
    opacity: 1;
    box-shadow: 0 8px 16px rgba(255, 159, 243, 0.4);
  }
  25% {
    opacity: 0.7;
    box-shadow: 0 12px 24px rgba(255, 159, 243, 0.6);
  }
  50% {
    opacity: 0.9;
    box-shadow: 0 6px 12px rgba(255, 159, 243, 0.3);
  }
  75% {
    opacity: 0.8;
    box-shadow: 0 10px 20px rgba(255, 159, 243, 0.5);
  }
}

/* 高亮出现动画 */
@keyframes duplicateAppear {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 高亮消失动画 */
@keyframes duplicateDisappear {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.8);
  }
}

/* ===== 特殊状态 ===== */

/* 新发现的重复 */
.duplicateNew {
  animation: duplicateAppear var(--duplicate-transition-slow) ease-out;
}

/* 即将移除的重复 */
.duplicateRemoving {
  animation: duplicateDisappear var(--duplicate-transition-slow) ease-in;
}

/* 选中状态 */
.duplicateSelected {
  outline: 3px solid #007bff;
  outline-offset: 2px;
  z-index: 10;
}

/* 焦点状态（键盘导航） */
.duplicateFocused {
  outline: 3px solid #28a745;
  outline-offset: 2px;
  outline-style: dashed;
}

/* ===== 主题变体 ===== */

/* 暗色主题 */
.duplicateHighlight[data-theme="dark"] {
  --duplicate-primary: #ff7979;
  --duplicate-secondary: #fdcb6e;
  --duplicate-tertiary: #74b9ff;
  --duplicate-quaternary: #fd79a8;
  
  --duplicate-shadow-light: 0 2px 4px rgba(255, 255, 255, 0.1);
  --duplicate-shadow-medium: 0 4px 8px rgba(255, 255, 255, 0.15);
  --duplicate-shadow-strong: 0 6px 12px rgba(255, 255, 255, 0.2);
}

/* 高对比度主题 */
.duplicateHighlight[data-theme="high-contrast"] {
  --duplicate-primary: #ff0000;
  --duplicate-secondary: #ffff00;
  --duplicate-tertiary: #00ffff;
  --duplicate-quaternary: #ff00ff;
  
  --duplicate-alpha-light: 0.4;
  --duplicate-alpha-medium: 0.6;
  --duplicate-alpha-strong: 0.8;
  --duplicate-alpha-intense: 1;
  
  --duplicate-border-width: 3px;
}

/* 柔和主题 */
.duplicateHighlight[data-theme="soft"] {
  --duplicate-primary: #fab1a0;
  --duplicate-secondary: #f39c12;
  --duplicate-tertiary: #81ecec;
  --duplicate-quaternary: #fd79a8;
  
  --duplicate-alpha-light: 0.15;
  --duplicate-alpha-medium: 0.25;
  --duplicate-alpha-strong: 0.35;
  --duplicate-alpha-intense: 0.5;
  
  --duplicate-transition-normal: 0.5s;
}

/* ===== 响应式设计 ===== */

/* 小屏幕设备 */
@media (max-width: 768px) {
  .duplicateHighlight {
    --duplicate-border-width: 1px;
    --duplicate-border-radius: 2px;
    --duplicate-transition-normal: 0.2s;
  }
  
  .duplicateLight:hover,
  .duplicateMedium:hover,
  .duplicateHeavy:hover,
  .duplicateExtreme:hover {
    transform: none; /* 移动设备上禁用hover变换 */
  }
}

/* 大屏幕设备 */
@media (min-width: 1200px) {
  .duplicateHighlight {
    --duplicate-border-width: 3px;
    --duplicate-border-radius: 6px;
  }
}

/* ===== 无障碍支持 ===== */

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .duplicateHighlight * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 高对比度偏好 */
@media (prefers-contrast: high) {
  .duplicateHighlight {
    --duplicate-border-width: 3px;
    --duplicate-alpha-light: 0.5;
    --duplicate-alpha-medium: 0.7;
    --duplicate-alpha-strong: 0.9;
    --duplicate-alpha-intense: 1;
  }
}

/* ===== 工具类 ===== */

/* 禁用高亮 */
.duplicateDisabled {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  animation: none !important;
}

/* 临时高亮（用于预览） */
.duplicatePreview {
  background-color: rgba(108, 92, 231, 0.3);
  border: 2px dashed rgba(108, 92, 231, 0.6);
  border-radius: var(--duplicate-border-radius);
  animation: duplicatePulse 1s infinite;
}

/* 错误状态 */
.duplicateError {
  background-color: rgba(231, 76, 60, 0.3);
  border: 2px solid rgba(231, 76, 60, 0.8);
  border-radius: var(--duplicate-border-radius);
  animation: duplicateIntense 0.5s 3;
}
