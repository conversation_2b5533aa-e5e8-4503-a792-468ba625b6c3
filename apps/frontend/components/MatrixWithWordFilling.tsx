/**
 * 带填词功能的矩阵组件
 * 🎯 核心价值：集成矩阵显示和词语填充功能，提供完整的交互体验
 * 📦 功能范围：矩阵渲染、双击激活、词语选择、预览确认
 * 🔄 架构设计：组合Matrix组件和WordSelectionModal，管理填词状态
 */

'use client';

import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useMatrixStore } from '../core/matrix/MatrixStore';
import type { Coordinate, MatrixConfig } from '../core/matrix/MatrixTypes';
import { useWordFillingManager } from '../core/word-library/WordFillingManager';
import type { WordEntry } from '../core/word-library/WordLibraryTypes';
import { useWordLibrary } from '../core/word-library/useWordLibrary';
import Matrix from './Matrix';
import WordSelectionModal from './WordSelectionModal';

// ===== 类型定义 =====

interface MatrixWithWordFillingProps {
  /** 自定义配置覆盖 */
  configOverride?: Partial<MatrixConfig>;

  /** 容器样式 */
  className?: string;
  style?: React.CSSProperties;

  /** 是否启用填词功能 */
  enableWordFilling?: boolean;

  /** 事件回调 */
  onCellClick?: (coordinate: Coordinate, event: React.MouseEvent) => void;
  onWordFilled?: (coordinate: Coordinate, word: WordEntry) => void;
  onWordRemoved?: (coordinate: Coordinate, word: WordEntry) => void;
  onModeChange?: (mode: string) => void;
}

// ===== 主组件 =====

export const MatrixWithWordFilling: React.FC<MatrixWithWordFillingProps> = ({
  configOverride,
  className = '',
  style,
  enableWordFilling = true,
  onCellClick,
  onWordFilled,
  onWordRemoved,
  onModeChange,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  // 状态管理
  const [modalState, setModalState] = useState<{
    isOpen: boolean;
    position: { x: number; y: number };
    coordinate: Coordinate;
  }>({
    isOpen: false,
    position: { x: 0, y: 0 },
    coordinate: { x: 0, y: 0 },
  });

  // Hooks
  const {
    startSession,
    endSession,
    selectWord,
    confirmSelection,
    cancelSelection,
    getSession,
  } = useWordFillingManager();

  const { bindWordToCell, unbindWordFromCell } = useWordLibrary();
  const { getCellData } = useMatrixStore();

  // 处理单元格双击 - 激活填词模式
  const handleCellDoubleClick = useCallback((coordinate: Coordinate, event: React.MouseEvent) => {
    if (!enableWordFilling) return;

    // 计算模态框位置
    const rect = (event.target as HTMLElement).getBoundingClientRect();
    const position = {
      x: rect.left + rect.width / 2,
      y: rect.bottom,
    };

    // 启动填词会话
    startSession(coordinate);

    // 显示词语选择模态框
    setModalState({
      isOpen: true,
      position,
      coordinate,
    });

    console.log(`激活填词模式: (${coordinate.x}, ${coordinate.y})`);
  }, [enableWordFilling, startSession]);

  // 处理词语选择
  const handleWordSelect = useCallback(async (word: WordEntry) => {
    const session = getSession();
    if (!session) return;

    try {
      // 选择词语（会自动预览）
      selectWord(word);

      // 立即确认选择（可以根据配置决定是否需要额外确认步骤）
      const confirmed = confirmSelection();

      if (confirmed) {
        // 绑定词语到单元格
        const result = await bindWordToCell(word.id, session.coordinate);

        if (result.success) {
          console.log(`词语 "${word.text}" 已填入 (${session.coordinate.x}, ${session.coordinate.y})`);
          onWordFilled?.(session.coordinate, word);
        } else {
          console.error('词语绑定失败:', result.error);
          alert(`填词失败: ${result.error}`);
        }
      }
    } catch (error) {
      console.error('词语选择处理失败:', error);
      alert('填词操作失败，请重试');
    } finally {
      // 关闭模态框和结束会话
      handleModalClose();
    }
  }, [getSession, selectWord, confirmSelection, bindWordToCell, onWordFilled]);

  // 处理模态框关闭
  const handleModalClose = useCallback(() => {
    setModalState(prev => ({ ...prev, isOpen: false }));
    cancelSelection();
    endSession();
  }, [cancelSelection, endSession]);

  // 处理单元格点击
  const handleCellClick = useCallback((coordinate: Coordinate, event: React.MouseEvent) => {
    // 如果是右键点击且单元格有词语，显示删除选项
    if (event.button === 2) { // 右键
      const cellData = getCellData(coordinate.x, coordinate.y);
      if (cellData?.wordId) {
        event.preventDefault();
        handleWordRemove(coordinate);
      }
    }

    onCellClick?.(coordinate, event);
  }, [getCellData, onCellClick]);

  // 处理词语删除
  const handleWordRemove = useCallback(async (coordinate: Coordinate) => {
    const cellData = getCellData(coordinate.x, coordinate.y);
    if (!cellData?.wordId) return;

    const confirmed = confirm(`确定要删除单元格 (${coordinate.x}, ${coordinate.y}) 中的词语吗？`);
    if (!confirmed) return;

    try {
      const result = await unbindWordFromCell(coordinate);

      if (result.success && result.word) {
        console.log(`词语 "${result.word.text}" 已从 (${coordinate.x}, ${coordinate.y}) 删除`);
        onWordRemoved?.(coordinate, result.word);
      } else {
        console.error('词语删除失败:', result.error);
        alert(`删除失败: ${result.error}`);
      }
    } catch (error) {
      console.error('词语删除处理失败:', error);
      alert('删除操作失败，请重试');
    }
  }, [getCellData, unbindWordFromCell, onWordRemoved]);

  // 键盘快捷键处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // 如果模态框打开，让模态框处理键盘事件
      if (modalState.isOpen) {
        if (event.key === 'Escape') {
          event.preventDefault();
          handleModalClose();
        }
        return;
      }

      // 全局快捷键（仅在模态框关闭时生效）
      switch (event.key) {
        case 'z':
          if (event.ctrlKey && !event.shiftKey) {
            event.preventDefault();
            // TODO: 实现撤销功能
            console.log('撤销操作');
          } else if (event.ctrlKey && event.shiftKey) {
            event.preventDefault();
            // TODO: 实现重做功能
            console.log('重做操作');
          }
          break;

        case 'y':
          if (event.ctrlKey) {
            event.preventDefault();
            // TODO: 实现重做功能（Ctrl+Y）
            console.log('重做操作');
          }
          break;

        case 'f':
          if (event.ctrlKey) {
            event.preventDefault();
            // TODO: 打开全局搜索
            console.log('打开搜索');
          }
          break;

        case 'n':
          if (event.ctrlKey) {
            event.preventDefault();
            // TODO: 新建词语
            console.log('新建词语');
          }
          break;

        case 's':
          if (event.ctrlKey) {
            event.preventDefault();
            // TODO: 保存当前状态
            console.log('保存状态');
          }
          break;

        case 'Delete':
        case 'Backspace':
          // TODO: 删除选中的单元格中的词语
          console.log('删除选中词语');
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [modalState.isOpen, handleModalClose]);

  // 右键菜单处理
  const handleContextMenu = useCallback((event: React.MouseEvent) => {
    // 阻止默认右键菜单，我们会在handleCellClick中处理
    event.preventDefault();
  }, []);

  return (
    <div
      ref={containerRef}
      className={`matrix-with-word-filling ${className}`}
      style={style}
      onContextMenu={handleContextMenu}
    >
      {/* 矩阵组件 */}
      <Matrix
        configOverride={{
          ...configOverride,
          // 启用词语填充相关的配置
          enableWordFilling: true,
        }}
        onCellClick={handleCellClick}
        onCellDoubleClick={handleCellDoubleClick}
        onModeChange={onModeChange}
        className="word-filling-matrix"
      />

      {/* 词语选择模态框 */}
      {modalState.isOpen && (
        <WordSelectionModal
          isOpen={modalState.isOpen}
          onClose={handleModalClose}
          onWordSelect={handleWordSelect}
          position={modalState.position}
          cellCoordinate={modalState.coordinate}
          className="word-selection-modal"
        />
      )}

      {/* 填词状态指示器 */}
      {enableWordFilling && (
        <div className="absolute top-2 right-2 text-xs text-gray-500 bg-white px-2 py-1 rounded shadow">
          双击单元格填词 | 右键删除词语
        </div>
      )}
    </div>
  );
};

export default MatrixWithWordFilling;
