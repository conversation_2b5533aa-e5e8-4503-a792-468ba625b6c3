/**
 * 词语批量操作组件
 * 🎯 核心价值：提供词语的批量导入、导出、删除等操作功能
 * 📦 功能范围：CSV导入导出、批量删除、操作进度显示、错误处理
 * 🔄 架构设计：基于模态对话框的批量操作界面，支持文件处理和进度反馈
 */

'use client';

import React, { useCallback, useRef, useState } from 'react';
import type { BasicColorType } from '../core/matrix/MatrixTypes';
import { useWordLibrary } from '../core/word-library/useWordLibrary';
import type { BatchOperationResult, ColorLevel, WordEntry } from '../core/word-library/WordLibraryTypes';

// ===== 类型定义 =====

interface WordBatchOperationsProps {
  isOpen: boolean;
  onClose: () => void;
  mode: 'import' | 'export' | 'delete';
  selectedWords?: WordEntry[];
  defaultCategory?: { color: BasicColorType; level: ColorLevel };
}

interface ImportPreviewItem {
  text: string;
  color: BasicColorType;
  level: ColorLevel;
  isValid: boolean;
  errors: string[];
}

// ===== 主组件 =====

export const WordBatchOperations: React.FC<WordBatchOperationsProps> = ({
  isOpen,
  onClose,
  mode,
  selectedWords = [],
  defaultCategory,
}) => {
  const { batchAddWords, batchRemoveWords, exportToCsv, importFromCsv } = useWordLibrary();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 状态管理
  const [isProcessing, setIsProcessing] = useState(false);
  const [importPreview, setImportPreview] = useState<ImportPreviewItem[]>([]);
  const [importText, setImportText] = useState('');
  const [result, setResult] = useState<BatchOperationResult | null>(null);

  // 处理文件选择
  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setImportText(content);
      parseImportData(content);
    };
    reader.readAsText(file, 'UTF-8');
  }, []);

  // 解析导入数据
  const parseImportData = useCallback((csvData: string) => {
    const lines = csvData.split('\n').filter(line => line.trim());
    const preview: ImportPreviewItem[] = [];

    // 跳过标题行
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      const [text, color, level] = line.split(',').map(s => s.trim());
      const errors: string[] = [];

      // 验证数据
      if (!text) errors.push('词语不能为空');
      if (!color || !['red', 'cyan', 'yellow', 'purple', 'orange', 'green', 'blue', 'pink', 'black'].includes(color)) {
        errors.push('颜色无效');
      }
      if (!level || !['1', '2', '3', '4'].includes(level)) {
        errors.push('等级无效');
      }
      if (text && !/^[\u4e00-\u9fff]+$/.test(text)) {
        errors.push('只能包含中文字符');
      }
      if (text && text.length > 10) {
        errors.push('词语长度不能超过10个字符');
      }

      preview.push({
        text: text || '',
        color: (color as BasicColorType) || 'red',
        level: parseInt(level) as ColorLevel || 1,
        isValid: errors.length === 0,
        errors,
      });
    }

    setImportPreview(preview);
  }, []);

  // 执行导入
  const handleImport = useCallback(async () => {
    if (importPreview.length === 0) return;

    setIsProcessing(true);
    try {
      const validItems = importPreview.filter(item => item.isValid);
      const words = validItems.map(item => ({
        text: item.text,
        category: { color: item.color, level: item.level },
        validation: { isValid: true }, // 添加验证信息
      }));

      const result = await batchAddWords(words);
      setResult(result);
    } catch (error) {
      setResult({
        success: false,
        processed: 0,
        errors: [error instanceof Error ? error.message : '导入失败'],
      });
    } finally {
      setIsProcessing(false);
    }
  }, [importPreview, batchAddWords]);

  // 执行导出
  const handleExport = useCallback(() => {
    const csvData = exportToCsv();
    const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    link.setAttribute('href', url);
    link.setAttribute('download', `词库导出_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    onClose();
  }, [exportToCsv, onClose]);

  // 执行批量删除
  const handleBatchDelete = useCallback(async () => {
    if (selectedWords.length === 0) return;

    const confirmed = confirm(`确定要删除选中的 ${selectedWords.length} 个词语吗？此操作不可撤销。`);
    if (!confirmed) return;

    setIsProcessing(true);
    try {
      const wordIds = selectedWords.map(word => word.id);
      const result = await batchRemoveWords(wordIds);
      setResult(result);
    } catch (error) {
      setResult({
        success: false,
        processed: 0,
        errors: [error instanceof Error ? error.message : '删除失败'],
      });
    } finally {
      setIsProcessing(false);
    }
  }, [selectedWords, batchRemoveWords]);

  // 重置状态
  const handleReset = useCallback(() => {
    setImportText('');
    setImportPreview([]);
    setResult(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* 标题栏 */}
        <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">
            {mode === 'import' && '批量导入词语'}
            {mode === 'export' && '导出词库'}
            {mode === 'delete' && '批量删除词语'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* 导入模式 */}
          {mode === 'import' && (
            <div className="space-y-6">
              {!result && (
                <>
                  {/* 文件上传 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      选择CSV文件
                    </label>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept=".csv"
                      onChange={handleFileSelect}
                      className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                    />
                    <p className="mt-1 text-sm text-gray-500">
                      CSV格式：词语,颜色,等级（第一行为标题行）
                    </p>
                  </div>

                  {/* 手动输入 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      或手动输入CSV数据
                    </label>
                    <textarea
                      value={importText}
                      onChange={(e) => {
                        setImportText(e.target.value);
                        parseImportData(e.target.value);
                      }}
                      placeholder="词语,颜色,等级&#10;示例,red,1&#10;测试,blue,2"
                      className="w-full h-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  {/* 预览 */}
                  {importPreview.length > 0 && (
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-3">
                        导入预览 ({importPreview.filter(item => item.isValid).length}/{importPreview.length} 有效)
                      </h3>
                      <div className="max-h-64 overflow-y-auto border border-gray-200 rounded-lg">
                        <table className="w-full text-sm">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-3 py-2 text-left">词语</th>
                              <th className="px-3 py-2 text-left">颜色</th>
                              <th className="px-3 py-2 text-left">等级</th>
                              <th className="px-3 py-2 text-left">状态</th>
                            </tr>
                          </thead>
                          <tbody>
                            {importPreview.map((item, index) => (
                              <tr key={index} className={item.isValid ? 'bg-white' : 'bg-red-50'}>
                                <td className="px-3 py-2">{item.text}</td>
                                <td className="px-3 py-2">{item.color}</td>
                                <td className="px-3 py-2">{item.level}</td>
                                <td className="px-3 py-2">
                                  {item.isValid ? (
                                    <span className="text-green-600">✓ 有效</span>
                                  ) : (
                                    <span className="text-red-600">✗ {item.errors.join(', ')}</span>
                                  )}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}
                </>
              )}

              {/* 结果显示 */}
              {result && (
                <div className={`p-4 rounded-lg ${result.success ? 'bg-green-50' : 'bg-red-50'}`}>
                  <h3 className={`font-medium ${result.success ? 'text-green-800' : 'text-red-800'}`}>
                    {result.success ? '导入成功' : '导入完成（有错误）'}
                  </h3>
                  <p className="mt-1 text-sm text-gray-600">
                    成功处理: {result.processed} 个词语
                  </p>
                  {result.errors.length > 0 && (
                    <div className="mt-2">
                      <p className="text-sm text-red-600">错误信息:</p>
                      <ul className="mt-1 text-sm text-red-600 list-disc list-inside">
                        {result.errors.map((error, index) => (
                          <li key={index}>{error}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* 导出模式 */}
          {mode === 'export' && (
            <div className="text-center py-8">
              <p className="text-gray-600 mb-4">
                将导出所有词库数据为CSV格式文件
              </p>
              <button
                onClick={handleExport}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                下载CSV文件
              </button>
            </div>
          )}

          {/* 删除模式 */}
          {mode === 'delete' && (
            <div className="space-y-4">
              {!result && (
                <>
                  <p className="text-gray-600">
                    即将删除以下 {selectedWords.length} 个词语：
                  </p>
                  <div className="max-h-64 overflow-y-auto border border-gray-200 rounded-lg">
                    <div className="p-3 space-y-2">
                      {selectedWords.map((word) => (
                        <div key={word.id} className="flex items-center justify-between py-2 px-3 bg-gray-50 rounded">
                          <span>{word.text}</span>
                          <span className="text-sm text-gray-500">
                            {word.category.color} - 等级{word.category.level}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              )}

              {result && (
                <div className={`p-4 rounded-lg ${result.success ? 'bg-green-50' : 'bg-red-50'}`}>
                  <h3 className={`font-medium ${result.success ? 'text-green-800' : 'text-red-800'}`}>
                    {result.success ? '删除成功' : '删除完成（有错误）'}
                  </h3>
                  <p className="mt-1 text-sm text-gray-600">
                    成功删除: {result.processed} 个词语
                  </p>
                  {result.errors.length > 0 && (
                    <div className="mt-2">
                      <p className="text-sm text-red-600">错误信息:</p>
                      <ul className="mt-1 text-sm text-red-600 list-disc list-inside">
                        {result.errors.map((error, index) => (
                          <li key={index}>{error}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>

        {/* 操作按钮 */}
        <div className="px-6 py-4 border-t border-gray-200 flex justify-end gap-3">
          {mode === 'import' && !result && (
            <>
              <button
                onClick={handleReset}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                重置
              </button>
              <button
                onClick={handleImport}
                disabled={importPreview.filter(item => item.isValid).length === 0 || isProcessing}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                {isProcessing ? '导入中...' : '开始导入'}
              </button>
            </>
          )}

          {mode === 'delete' && !result && (
            <button
              onClick={handleBatchDelete}
              disabled={selectedWords.length === 0 || isProcessing}
              className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
            >
              {isProcessing ? '删除中...' : '确认删除'}
            </button>
          )}

          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
          >
            {result ? '完成' : '取消'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default WordBatchOperations;
