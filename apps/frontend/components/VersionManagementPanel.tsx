/**
 * 版本管理面板组件
 * 🎯 核心价值：提供完整的版本管理界面，支持版本创建、查看、比较、恢复
 * 📦 功能范围：版本列表、版本操作、版本比较、导入导出
 * 🔄 架构设计：基于React的模块化组件，集成WordLibraryStore
 */

'use client';

import React, { useState, useCallback, useMemo } from 'react';
import { useWordLibraryStore } from '../core/word-library/WordLibraryStore';
import type { WordVersion } from '../core/word-library/WordLibraryTypes';
import styles from './VersionManagementPanel.module.css';

// ===== 类型定义 =====

interface VersionManagementPanelProps {
  /** 是否显示面板 */
  isVisible: boolean;
  /** 关闭面板回调 */
  onClose: () => void;
  /** 面板标题 */
  title?: string;
}

interface VersionItemProps {
  version: WordVersion;
  isActive: boolean;
  onLoad: (versionId: string) => void;
  onDelete: (versionId: string) => void;
  onExport: (versionId: string) => void;
  onCompare: (versionId: string) => void;
}

// ===== 版本项组件 =====

const VersionItem: React.FC<VersionItemProps> = ({
  version,
  isActive,
  onLoad,
  onDelete,
  onExport,
  onCompare,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const formatDate = useCallback((date: Date) => {
    return new Date(date).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  }, []);

  const formatSize = useCallback((bindingCount: number) => {
    return `${bindingCount} 个绑定`;
  }, []);

  return (
    <div className={`${styles.versionItem} ${isActive ? styles.active : ''}`}>
      <div className={styles.versionHeader} onClick={() => setIsExpanded(!isExpanded)}>
        <div className={styles.versionInfo}>
          <h4 className={styles.versionName}>{version.name}</h4>
          <span className={styles.versionDate}>{formatDate(version.createdAt)}</span>
          {isActive && <span className={styles.activeLabel}>当前版本</span>}
        </div>
        <div className={styles.versionStats}>
          <span className={styles.stat}>{formatSize(version.metadata.totalBindings)}</span>
          <span className={styles.stat}>覆盖率: {(version.metadata.coverageRate * 100).toFixed(1)}%</span>
          <button
            className={styles.expandButton}
            aria-label={isExpanded ? '收起' : '展开'}
          >
            {isExpanded ? '▼' : '▶'}
          </button>
        </div>
      </div>

      {isExpanded && (
        <div className={styles.versionDetails}>
          {version.description && (
            <p className={styles.versionDescription}>{version.description}</p>
          )}
          
          <div className={styles.versionMetadata}>
            <div className={styles.metadataItem}>
              <span className={styles.metadataLabel}>词语数量:</span>
              <span className={styles.metadataValue}>{version.metadata.wordCount}</span>
            </div>
            <div className={styles.metadataItem}>
              <span className={styles.metadataLabel}>重复数量:</span>
              <span className={styles.metadataValue}>{version.metadata.duplicateCount}</span>
            </div>
            <div className={styles.metadataItem}>
              <span className={styles.metadataLabel}>版本ID:</span>
              <span className={styles.metadataValue}>{version.id}</span>
            </div>
          </div>

          <div className={styles.versionActions}>
            {!isActive && (
              <button
                className={styles.actionButton}
                onClick={() => onLoad(version.id)}
              >
                加载版本
              </button>
            )}
            <button
              className={styles.actionButton}
              onClick={() => onExport(version.id)}
            >
              导出
            </button>
            <button
              className={styles.actionButton}
              onClick={() => onCompare(version.id)}
            >
              比较
            </button>
            <button
              className={`${styles.actionButton} ${styles.deleteButton}`}
              onClick={() => onDelete(version.id)}
            >
              删除
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

// ===== 主组件 =====

export const VersionManagementPanel: React.FC<VersionManagementPanelProps> = ({
  isVisible,
  onClose,
  title = '版本管理',
}) => {
  const {
    wordVersions,
    currentVersionId,
    saveWordVersion,
    loadWordVersion,
    deleteWordVersion,
    exportVersion,
    importVersion,
    compareVersions,
  } = useWordLibraryStore();

  const [newVersionName, setNewVersionName] = useState('');
  const [newVersionDescription, setNewVersionDescription] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [compareVersionId, setCompareVersionId] = useState<string | null>(null);
  const [comparisonResult, setComparisonResult] = useState<any>(null);

  // 排序版本列表（最新的在前）
  const sortedVersions = useMemo(() => {
    return [...wordVersions].sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
  }, [wordVersions]);

  // 创建新版本
  const handleCreateVersion = useCallback(async () => {
    if (!newVersionName.trim()) {
      alert('请输入版本名称');
      return;
    }

    setIsCreating(true);
    try {
      await saveWordVersion(newVersionName.trim(), newVersionDescription.trim() || undefined);
      setNewVersionName('');
      setNewVersionDescription('');
      alert('版本创建成功！');
    } catch (error) {
      alert(`创建版本失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsCreating(false);
    }
  }, [newVersionName, newVersionDescription, saveWordVersion]);

  // 加载版本
  const handleLoadVersion = useCallback(async (versionId: string) => {
    if (!confirm('加载版本将覆盖当前数据，确定继续吗？')) {
      return;
    }

    setIsLoading(true);
    try {
      await loadWordVersion(versionId);
      alert('版本加载成功！');
    } catch (error) {
      alert(`加载版本失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsLoading(false);
    }
  }, [loadWordVersion]);

  // 删除版本
  const handleDeleteVersion = useCallback(async (versionId: string) => {
    if (!confirm('确定要删除这个版本吗？此操作不可撤销。')) {
      return;
    }

    try {
      await deleteWordVersion(versionId);
      alert('版本删除成功！');
    } catch (error) {
      alert(`删除版本失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }, [deleteWordVersion]);

  // 导出版本
  const handleExportVersion = useCallback(async (versionId: string) => {
    try {
      const jsonData = await exportVersion(versionId);
      const blob = new Blob([jsonData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `word-library-version-${versionId}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      alert(`导出版本失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }, [exportVersion]);

  // 导入版本
  const handleImportVersion = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const text = await file.text();
      const versionId = await importVersion(text);
      alert(`版本导入成功！版本ID: ${versionId}`);
    } catch (error) {
      alert(`导入版本失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
    
    // 清空文件输入
    event.target.value = '';
  }, [importVersion]);

  // 比较版本
  const handleCompareVersion = useCallback(async (versionId: string) => {
    if (!currentVersionId) {
      alert('请先选择一个当前版本');
      return;
    }

    if (versionId === currentVersionId) {
      alert('不能与当前版本比较');
      return;
    }

    try {
      const result = await compareVersions(currentVersionId, versionId);
      setCompareVersionId(versionId);
      setComparisonResult(result);
    } catch (error) {
      alert(`版本比较失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }, [currentVersionId, compareVersions]);

  if (!isVisible) {
    return null;
  }

  return (
    <div className={styles.overlay}>
      <div className={styles.panel}>
        <div className={styles.header}>
          <h2 className={styles.title}>{title}</h2>
          <button className={styles.closeButton} onClick={onClose}>
            ✕
          </button>
        </div>

        <div className={styles.content}>
          {/* 创建新版本 */}
          <div className={styles.createSection}>
            <h3>创建新版本</h3>
            <div className={styles.createForm}>
              <input
                type="text"
                placeholder="版本名称"
                value={newVersionName}
                onChange={(e) => setNewVersionName(e.target.value)}
                className={styles.input}
              />
              <textarea
                placeholder="版本描述（可选）"
                value={newVersionDescription}
                onChange={(e) => setNewVersionDescription(e.target.value)}
                className={styles.textarea}
                rows={2}
              />
              <button
                onClick={handleCreateVersion}
                disabled={isCreating || !newVersionName.trim()}
                className={styles.createButton}
              >
                {isCreating ? '创建中...' : '创建版本'}
              </button>
            </div>
          </div>

          {/* 导入版本 */}
          <div className={styles.importSection}>
            <h3>导入版本</h3>
            <input
              type="file"
              accept=".json"
              onChange={handleImportVersion}
              className={styles.fileInput}
            />
          </div>

          {/* 版本列表 */}
          <div className={styles.versionList}>
            <h3>版本历史 ({sortedVersions.length})</h3>
            {sortedVersions.length === 0 ? (
              <p className={styles.emptyMessage}>暂无版本</p>
            ) : (
              <div className={styles.versionItems}>
                {sortedVersions.map((version) => (
                  <VersionItem
                    key={version.id}
                    version={version}
                    isActive={version.id === currentVersionId}
                    onLoad={handleLoadVersion}
                    onDelete={handleDeleteVersion}
                    onExport={handleExportVersion}
                    onCompare={handleCompareVersion}
                  />
                ))}
              </div>
            )}
          </div>

          {/* 版本比较结果 */}
          {comparisonResult && (
            <div className={styles.comparisonSection}>
              <h3>版本比较结果</h3>
              <div className={styles.comparisonStats}>
                <div className={styles.statItem}>
                  <span className={styles.statLabel}>总变更:</span>
                  <span className={styles.statValue}>{comparisonResult.statistics.totalChanges}</span>
                </div>
                <div className={styles.statItem}>
                  <span className={styles.statLabel}>新增:</span>
                  <span className={styles.statValue}>{comparisonResult.statistics.addedBindings}</span>
                </div>
                <div className={styles.statItem}>
                  <span className={styles.statLabel}>删除:</span>
                  <span className={styles.statValue}>{comparisonResult.statistics.removedBindings}</span>
                </div>
                <div className={styles.statItem}>
                  <span className={styles.statLabel}>修改:</span>
                  <span className={styles.statValue}>{comparisonResult.statistics.modifiedBindings}</span>
                </div>
              </div>
              <button
                onClick={() => setComparisonResult(null)}
                className={styles.closeComparisonButton}
              >
                关闭比较
              </button>
            </div>
          )}
        </div>

        {isLoading && (
          <div className={styles.loadingOverlay}>
            <div className={styles.loadingMessage}>加载中...</div>
          </div>
        )}
      </div>
    </div>
  );
};

export default VersionManagementPanel;
