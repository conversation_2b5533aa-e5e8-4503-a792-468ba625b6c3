/**
 * 词库管理面板组件
 * 🎯 核心价值：提供完整的词库管理界面，支持分类浏览、搜索过滤、词语操作
 * 📦 功能范围：分类标签页、搜索框、词语列表、操作按钮、统计信息
 * 🔄 架构设计：基于React的响应式组件，支持键盘导航和无障碍访问
 */

'use client';

import React, { useCallback, useMemo, useState } from 'react';
import type { BasicColorType } from '../core/matrix/MatrixTypes';
import { useWordLibrary, useWordLibraryStats, useWordSearch } from '../core/word-library/useWordLibrary';
import type { ColorLevel, WordEntry } from '../core/word-library/WordLibraryTypes';

// ===== 类型定义 =====

interface WordLibraryPanelProps {
  className?: string;
  onWordSelect?: (word: WordEntry) => void;
  onWordEdit?: (word: WordEntry) => void;
  selectedWordId?: string;
  isVisible?: boolean;
}

interface CategoryTabProps {
  color: BasicColorType;
  level: ColorLevel;
  isActive: boolean;
  wordCount: number;
  onClick: () => void;
}

// ===== 颜色配置 =====

const COLOR_CONFIG: Record<BasicColorType, { name: string; hex: string }> = {
  red: { name: '红色', hex: '#ef4444' },
  cyan: { name: '青色', hex: '#06b6d4' },
  yellow: { name: '黄色', hex: '#eab308' },
  purple: { name: '紫色', hex: '#a855f7' },
  orange: { name: '橙色', hex: '#f97316' },
  green: { name: '绿色', hex: '#22c55e' },
  blue: { name: '蓝色', hex: '#3b82f6' },
  pink: { name: '粉色', hex: '#ec4899' },
  black: { name: '黑色', hex: '#374151' },
};

const LEVEL_CONFIG: Record<ColorLevel, string> = {
  1: '等级1',
  2: '等级2',
  3: '等级3',
  4: '等级4',
};

// ===== 子组件 =====

const CategoryTab: React.FC<CategoryTabProps> = ({ color, level, isActive, wordCount, onClick }) => {
  const colorConfig = COLOR_CONFIG[color];

  return (
    <button
      onClick={onClick}
      className={`
        flex items-center gap-2 px-3 py-2 rounded-lg border transition-all duration-200
        ${isActive
          ? 'bg-blue-50 border-blue-300 text-blue-700'
          : 'bg-white border-gray-200 text-gray-600 hover:bg-gray-50'
        }
      `}
    >
      <div
        className="w-3 h-3 rounded-full"
        style={{ backgroundColor: colorConfig.hex }}
      />
      <span className="text-sm font-medium">
        {colorConfig.name} {LEVEL_CONFIG[level]}
      </span>
      <span className="text-xs bg-gray-100 px-2 py-1 rounded-full">
        {wordCount}
      </span>
    </button>
  );
};

const WordItem: React.FC<{
  word: WordEntry;
  isSelected: boolean;
  onSelect: () => void;
  onEdit: () => void;
  onDelete: () => void;
}> = ({ word, isSelected, onSelect, onEdit, onDelete }) => {
  return (
    <div
      className={`
        flex items-center justify-between p-3 rounded-lg border cursor-pointer transition-all duration-200
        ${isSelected
          ? 'bg-blue-50 border-blue-300'
          : 'bg-white border-gray-200 hover:bg-gray-50'
        }
      `}
      onClick={onSelect}
    >
      <div className="flex-1">
        <div className="font-medium text-gray-900">{word.text}</div>
        <div className="text-sm text-gray-500">
          使用次数: {word.metadata.usageCount} |
          创建时间: {word.metadata.createdAt.toLocaleDateString()}
        </div>
      </div>
      <div className="flex gap-2">
        <button
          onClick={(e) => {
            e.stopPropagation();
            onEdit();
          }}
          className="px-2 py-1 text-sm text-blue-600 hover:bg-blue-100 rounded"
        >
          编辑
        </button>
        <button
          onClick={(e) => {
            e.stopPropagation();
            onDelete();
          }}
          className="px-2 py-1 text-sm text-red-600 hover:bg-red-100 rounded"
        >
          删除
        </button>
      </div>
    </div>
  );
};

const SearchBar: React.FC<{
  searchText: string;
  onSearchChange: (text: string) => void;
  onClear: () => void;
}> = ({ searchText, onSearchChange, onClear }) => {
  return (
    <div className="relative">
      <input
        type="text"
        value={searchText}
        onChange={(e) => onSearchChange(e.target.value)}
        placeholder="搜索词语..."
        className="w-full px-4 py-2 pl-10 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
      />
      <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
        🔍
      </div>
      {searchText && (
        <button
          onClick={onClear}
          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
        >
          ✕
        </button>
      )}
    </div>
  );
};

// ===== 主组件 =====

export const WordLibraryPanel: React.FC<WordLibraryPanelProps> = ({
  className = '',
  onWordSelect,
  onWordEdit,
  selectedWordId,
  isVisible = true,
}) => {
  const { removeWord, getWordsByCategory } = useWordLibrary();
  const { stats } = useWordLibraryStats();
  const [activeCategory, setActiveCategory] = useState<{ color: BasicColorType; level: ColorLevel }>({
    color: 'red',
    level: 1,
  });

  // 搜索状态
  const {
    searchText,
    setSearchText,
    results: searchResults,
    hasResults,
  } = useWordSearch({
    color: activeCategory.color,
    level: activeCategory.level,
  });

  // 当前分类的词语
  const categoryWords = useMemo(() => {
    if (searchText) {
      return searchResults;
    }
    return getWordsByCategory(activeCategory.color, activeCategory.level);
  }, [activeCategory, searchText, searchResults, getWordsByCategory]);

  // 处理词语删除
  const handleDeleteWord = useCallback(async (wordId: string) => {
    if (confirm('确定要删除这个词语吗？')) {
      const result = await removeWord(wordId);
      if (!result.success) {
        alert(`删除失败: ${result.error}`);
      }
    }
  }, [removeWord]);

  // 处理分类切换
  const handleCategoryChange = useCallback((color: BasicColorType, level: ColorLevel) => {
    setActiveCategory({ color, level });
    setSearchText(''); // 清除搜索
  }, [setSearchText]);

  if (!isVisible) {
    return null;
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* 标题栏 */}
      <div className="px-4 py-3 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">词库管理</h2>
          <div className="text-sm text-gray-500">
            总词语数: {stats.totalWords}
          </div>
        </div>
      </div>

      {/* 搜索栏 */}
      <div className="p-4 border-b border-gray-200">
        <SearchBar
          searchText={searchText}
          onSearchChange={setSearchText}
          onClear={() => setSearchText('')}
        />
      </div>

      {/* 分类标签页 */}
      <div className="p-4 border-b border-gray-200">
        <div className="mb-3">
          <h3 className="text-sm font-medium text-gray-700 mb-2">颜色分类</h3>
          <div className="flex flex-wrap gap-2">
            {(Object.keys(COLOR_CONFIG) as BasicColorType[]).map((color) => (
              <button
                key={color}
                onClick={() => handleCategoryChange(color, activeCategory.level)}
                className={`
                  flex items-center gap-2 px-3 py-1 rounded-full text-sm transition-all duration-200
                  ${activeCategory.color === color
                    ? 'bg-blue-100 text-blue-700 border border-blue-300'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }
                `}
              >
                <div
                  className="w-2 h-2 rounded-full"
                  style={{ backgroundColor: COLOR_CONFIG[color].hex }}
                />
                {COLOR_CONFIG[color].name}
                <span className="text-xs">({stats.wordsByColor[color]})</span>
              </button>
            ))}
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-2">等级分类</h3>
          <div className="flex gap-2">
            {([1, 2, 3, 4] as ColorLevel[]).map((level) => (
              <button
                key={level}
                onClick={() => handleCategoryChange(activeCategory.color, level)}
                className={`
                  px-3 py-1 rounded-full text-sm transition-all duration-200
                  ${activeCategory.level === level
                    ? 'bg-blue-100 text-blue-700 border border-blue-300'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }
                `}
              >
                {LEVEL_CONFIG[level]}
                <span className="ml-1 text-xs">({stats.wordsByLevel[level]})</span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* 词语列表 */}
      <div className="p-4">
        <div className="mb-3 flex items-center justify-between">
          <h3 className="text-sm font-medium text-gray-700">
            {searchText ? `搜索结果 (${categoryWords.length})` :
              `${COLOR_CONFIG[activeCategory.color].name} ${LEVEL_CONFIG[activeCategory.level]} (${categoryWords.length})`}
          </h3>
          <div className="flex gap-2">
            <button
              onClick={() => {/* TODO: 打开添加词语对话框 */ }}
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              + 添加词语
            </button>
            <button
              onClick={() => {/* TODO: 打开批量导入对话框 */ }}
              className="text-sm text-green-600 hover:text-green-800"
            >
              批量导入
            </button>
          </div>
        </div>

        <div className="space-y-2 max-h-96 overflow-y-auto">
          {categoryWords.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              {searchText ? '没有找到匹配的词语' : '该分类暂无词语'}
            </div>
          ) : (
            categoryWords.map((word) => (
              <WordItem
                key={word.id}
                word={word}
                isSelected={selectedWordId === word.id}
                onSelect={() => onWordSelect?.(word)}
                onEdit={() => onWordEdit?.(word)}
                onDelete={() => handleDeleteWord(word.id)}
              />
            ))
          )}
        </div>
      </div>

      {/* 统计信息 */}
      <div className="px-4 py-3 border-t border-gray-200 bg-gray-50">
        <div className="text-xs text-gray-600 space-y-1">
          <div>当前分类: {COLOR_CONFIG[activeCategory.color].name} {LEVEL_CONFIG[activeCategory.level]}</div>
          <div>词语总数: {stats.totalWords} | 分类覆盖率: {((stats.totalWords > 0 ? Object.values(stats.wordsByColor).filter(count => count > 0).length : 0) / 9 * 100).toFixed(1)}%</div>
        </div>
      </div>
    </div>
  );
};

export default WordLibraryPanel;
