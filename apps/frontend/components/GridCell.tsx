/**
 * 网格单元格组件
 * 🎯 核心价值：统一的单元格渲染和交互管理，支持词语填充功能
 * 📦 功能范围：单元格渲染、事件处理、词语状态显示、性能优化
 * 🔄 架构设计：基于React.memo的高性能组件，支持多种交互模式
 */

'use client';

import React, { useCallback, useMemo } from 'react';
import type { CellData, CellRenderData, Coordinate } from '../core/matrix/MatrixTypes';
import styles from './GridCell.module.css';

// ===== 类型定义 =====

interface GridCellProps {
  /** 单元格数据 */
  cellData: CellData;

  /** 渲染数据 */
  renderData: CellRenderData | null;

  /** 单元格位置 */
  coordinate: Coordinate;

  /** 是否启用词语填充功能 */
  enableWordFilling?: boolean;

  /** 事件回调 */
  onClick?: (coordinate: Coordinate, event: React.MouseEvent) => void;
  onDoubleClick?: (coordinate: Coordinate, event: React.MouseEvent) => void;
  onMouseEnter?: (coordinate: Coordinate, event: React.MouseEvent) => void;
  onMouseLeave?: (coordinate: Coordinate, event: React.MouseEvent) => void;
  onContextMenu?: (coordinate: Coordinate, event: React.MouseEvent) => void;

  /** 样式覆盖 */
  className?: string;
  style?: React.CSSProperties;
}

// ===== 主组件 =====

const GridCell: React.FC<GridCellProps> = ({
  cellData,
  renderData,
  coordinate,
  enableWordFilling = false,
  onClick,
  onDoubleClick,
  onMouseEnter,
  onMouseLeave,
  onContextMenu,
  className = '',
  style,
}) => {
  // 计算单元格样式
  const cellStyle = useMemo(() => {
    const baseStyle: React.CSSProperties = {
      position: 'absolute',
      left: `${coordinate.x * 34}px`, // 33px + 1px gap
      top: `${coordinate.y * 34}px`,
      width: '33px',
      height: '33px',
      border: '1px solid #e5e7eb',
      backgroundColor: renderData?.style?.backgroundColor || '#ffffff',
      color: renderData?.style?.color || '#000000',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      cursor: 'pointer',
      userSelect: 'none',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      whiteSpace: 'nowrap',
      borderRadius: '6px',
      transition: 'all 0.2s ease-in-out',
      ...renderData?.style, // 动态样式应该最后应用
      ...style, // 外部样式覆盖
    };

    // 词语相关的样式增强
    if (enableWordFilling) {
      // 选中状态
      if (cellData.isSelected) {
        baseStyle.boxShadow = '0 0 0 2px #3b82f6, 0 0 8px rgba(59, 130, 246, 0.3)';
        baseStyle.zIndex = 10;
      }

      // 悬停状态
      if (cellData.isHovered) {
        baseStyle.transform = 'scale(1.05)';
        baseStyle.zIndex = 5;
      }

      // 重复词语高亮
      if (cellData.isDuplicate) {
        baseStyle.border = '2px solid #ef4444';
        baseStyle.boxShadow = '0 0 8px rgba(239, 68, 68, 0.3)';
      }

      // 词语预览状态
      if (cellData.isWordPreview) {
        baseStyle.backgroundColor = '#fef3c7';
        baseStyle.color = '#92400e';
        baseStyle.border = '2px dashed #f59e0b';
        baseStyle.boxShadow = '0 0 12px rgba(245, 158, 11, 0.4)';
        baseStyle.animation = 'pulse 2s infinite';
      }

      // 有词语绑定的单元格
      if (cellData.wordId) {
        baseStyle.fontWeight = '600';
        baseStyle.border = '2px solid #10b981';
        baseStyle.boxShadow = '0 0 4px rgba(16, 185, 129, 0.2)';
      }
    }

    return baseStyle;
  }, [cellData, renderData, coordinate, enableWordFilling, style]);

  // 计算单元格类名
  const cellClassName = useMemo(() => {
    const classes = [styles.gridCell, renderData?.className || 'matrix-cell'];

    if (enableWordFilling) {
      if (cellData.isSelected) classes.push(styles.gridCellSelected);
      if (cellData.isHovered) classes.push(styles.gridCellHovered);
      if (cellData.isDuplicate) classes.push(styles.gridCellDuplicate);
      if (cellData.isWordPreview) classes.push(styles.gridCellPreview);
      if (cellData.wordId) classes.push(styles.gridCellHasWord);
    }

    if (className) classes.push(className);

    return classes.join(' ');
  }, [cellData, renderData, enableWordFilling, className]);

  // 计算显示内容
  const displayContent = useMemo(() => {
    // 如果有词语，优先显示词语
    if (cellData.word) {
      return cellData.word;
    }

    // 否则显示渲染数据的内容
    return renderData?.content || '';
  }, [cellData.word, renderData?.content]);

  // 事件处理器
  const handleClick = useCallback((event: React.MouseEvent) => {
    event.stopPropagation();
    onClick?.(coordinate, event);
  }, [coordinate, onClick]);

  const handleDoubleClick = useCallback((event: React.MouseEvent) => {
    event.stopPropagation();
    onDoubleClick?.(coordinate, event);
  }, [coordinate, onDoubleClick]);

  const handleMouseEnter = useCallback((event: React.MouseEvent) => {
    onMouseEnter?.(coordinate, event);
  }, [coordinate, onMouseEnter]);

  const handleMouseLeave = useCallback((event: React.MouseEvent) => {
    onMouseLeave?.(coordinate, event);
  }, [coordinate, onMouseLeave]);

  const handleContextMenu = useCallback((event: React.MouseEvent) => {
    if (enableWordFilling && cellData.wordId) {
      event.preventDefault();
      onContextMenu?.(coordinate, event);
    }
  }, [enableWordFilling, cellData.wordId, coordinate, onContextMenu]);

  return (
    <div
      data-x={coordinate.x}
      data-y={coordinate.y}
      data-cell-key={`${coordinate.x},${coordinate.y}`}
      data-has-word={!!cellData.wordId}
      data-word-id={cellData.wordId}
      className={cellClassName}
      style={cellStyle}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onContextMenu={handleContextMenu}
      role="gridcell"
      aria-label={`单元格 ${coordinate.x}, ${coordinate.y}${cellData.word ? `, 词语: ${cellData.word}` : ''}`}
      aria-selected={cellData.isSelected}
      tabIndex={cellData.isSelected ? 0 : -1}
    >
      {displayContent}
    </div>
  );
};

// ===== 性能优化 =====

const arePropsEqual = (prevProps: GridCellProps, nextProps: GridCellProps): boolean => {
  // 比较坐标
  if (prevProps.coordinate.x !== nextProps.coordinate.x ||
    prevProps.coordinate.y !== nextProps.coordinate.y) {
    return false;
  }

  // 比较单元格数据的关键属性
  const prevCell = prevProps.cellData;
  const nextCell = nextProps.cellData;

  if (prevCell.isSelected !== nextCell.isSelected ||
    prevCell.isHovered !== nextCell.isHovered ||
    prevCell.isDuplicate !== nextCell.isDuplicate ||
    prevCell.isWordPreview !== nextCell.isWordPreview ||
    prevCell.word !== nextCell.word ||
    prevCell.wordId !== nextCell.wordId) {
    return false;
  }

  // 比较渲染数据
  const prevRender = prevProps.renderData;
  const nextRender = nextProps.renderData;

  if (prevRender?.content !== nextRender?.content) {
    return false;
  }

  // 比较样式的关键属性
  if (prevRender?.style?.backgroundColor !== nextRender?.style?.backgroundColor ||
    prevRender?.style?.color !== nextRender?.style?.color) {
    return false;
  }

  // 比较其他props
  if (prevProps.enableWordFilling !== nextProps.enableWordFilling ||
    prevProps.className !== nextProps.className) {
    return false;
  }

  return true;
};

export default React.memo(GridCell, arePropsEqual);
