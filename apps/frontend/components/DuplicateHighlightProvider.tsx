/**
 * 重复词语高亮提供者组件
 * 🎯 核心价值：统一管理重复词语的视觉高亮效果
 * 📦 功能范围：高亮配置、主题切换、动画控制、无障碍支持
 * 🔄 架构设计：基于Context的全局状态管理，支持动态配置
 */

import React, { createContext, useContext, useCallback, useMemo, useState, useEffect } from 'react';
import type { DuplicateDetection, DuplicateGroup } from '../core/word-library/WordLibraryTypes';
import styles from './DuplicateHighlight.module.css';

// ===== 类型定义 =====

/** 高亮强度级别 */
export type HighlightIntensity = 'light' | 'medium' | 'heavy' | 'extreme';

/** 高亮主题 */
export type HighlightTheme = 'default' | 'dark' | 'high-contrast' | 'soft';

/** 高亮配置 */
export interface HighlightConfig {
  /** 是否启用高亮 */
  enabled: boolean;
  /** 主题 */
  theme: HighlightTheme;
  /** 是否启用动画 */
  enableAnimations: boolean;
  /** 是否启用hover效果 */
  enableHoverEffects: boolean;
  /** 自定义颜色 */
  customColors?: {
    light?: string;
    medium?: string;
    heavy?: string;
    extreme?: string;
  };
  /** 强度阈值 */
  intensityThresholds: {
    light: number;    // 2-3次
    medium: number;   // 4-5次
    heavy: number;    // 6-9次
    extreme: number;  // 10次以上
  };
}

/** 高亮状态 */
export interface HighlightState {
  /** 当前重复检测结果 */
  duplicateDetection: DuplicateDetection | null;
  /** 选中的重复词语 */
  selectedDuplicates: Set<string>;
  /** 焦点重复词语 */
  focusedDuplicate: string | null;
  /** 新发现的重复词语 */
  newDuplicates: Set<string>;
  /** 即将移除的重复词语 */
  removingDuplicates: Set<string>;
}

/** Context值类型 */
interface HighlightContextValue {
  config: HighlightConfig;
  state: HighlightState;
  actions: {
    updateConfig: (config: Partial<HighlightConfig>) => void;
    updateDuplicateDetection: (detection: DuplicateDetection) => void;
    selectDuplicate: (wordText: string) => void;
    deselectDuplicate: (wordText: string) => void;
    clearSelection: () => void;
    focusDuplicate: (wordText: string | null) => void;
    getHighlightClass: (wordText: string, usageCount: number) => string;
    getHighlightIntensity: (usageCount: number) => HighlightIntensity;
  };
}

// ===== 默认配置 =====

const DEFAULT_CONFIG: HighlightConfig = {
  enabled: true,
  theme: 'default',
  enableAnimations: true,
  enableHoverEffects: true,
  intensityThresholds: {
    light: 2,
    medium: 4,
    heavy: 6,
    extreme: 10,
  },
};

const DEFAULT_STATE: HighlightState = {
  duplicateDetection: null,
  selectedDuplicates: new Set(),
  focusedDuplicate: null,
  newDuplicates: new Set(),
  removingDuplicates: new Set(),
};

// ===== Context创建 =====

const HighlightContext = createContext<HighlightContextValue | null>(null);

// ===== Provider组件 =====

interface HighlightProviderProps {
  children: React.ReactNode;
  initialConfig?: Partial<HighlightConfig>;
}

export const DuplicateHighlightProvider: React.FC<HighlightProviderProps> = ({
  children,
  initialConfig = {},
}) => {
  const [config, setConfig] = useState<HighlightConfig>({
    ...DEFAULT_CONFIG,
    ...initialConfig,
  });

  const [state, setState] = useState<HighlightState>(DEFAULT_STATE);

  // ===== 配置更新 =====

  const updateConfig = useCallback((newConfig: Partial<HighlightConfig>) => {
    setConfig(prev => ({ ...prev, ...newConfig }));
  }, []);

  // ===== 状态更新 =====

  const updateDuplicateDetection = useCallback((detection: DuplicateDetection) => {
    setState(prev => {
      const newDuplicates = new Set<string>();
      const currentDuplicates = prev.duplicateDetection?.duplicateWords || new Set();

      // 识别新发现的重复词语
      detection.duplicateWords.forEach(wordText => {
        if (!currentDuplicates.has(wordText)) {
          newDuplicates.add(wordText);
        }
      });

      // 识别即将移除的重复词语
      const removingDuplicates = new Set<string>();
      currentDuplicates.forEach(wordText => {
        if (!detection.duplicateWords.has(wordText)) {
          removingDuplicates.add(wordText);
        }
      });

      return {
        ...prev,
        duplicateDetection: detection,
        newDuplicates,
        removingDuplicates,
      };
    });

    // 清理新发现和移除状态
    setTimeout(() => {
      setState(prev => ({
        ...prev,
        newDuplicates: new Set(),
        removingDuplicates: new Set(),
      }));
    }, 1000);
  }, []);

  const selectDuplicate = useCallback((wordText: string) => {
    setState(prev => ({
      ...prev,
      selectedDuplicates: new Set([...prev.selectedDuplicates, wordText]),
    }));
  }, []);

  const deselectDuplicate = useCallback((wordText: string) => {
    setState(prev => {
      const newSelected = new Set(prev.selectedDuplicates);
      newSelected.delete(wordText);
      return {
        ...prev,
        selectedDuplicates: newSelected,
      };
    });
  }, []);

  const clearSelection = useCallback(() => {
    setState(prev => ({
      ...prev,
      selectedDuplicates: new Set(),
    }));
  }, []);

  const focusDuplicate = useCallback((wordText: string | null) => {
    setState(prev => ({
      ...prev,
      focusedDuplicate: wordText,
    }));
  }, []);

  // ===== 高亮强度计算 =====

  const getHighlightIntensity = useCallback((usageCount: number): HighlightIntensity => {
    const { intensityThresholds } = config;

    if (usageCount >= intensityThresholds.extreme) {
      return 'extreme';
    } else if (usageCount >= intensityThresholds.heavy) {
      return 'heavy';
    } else if (usageCount >= intensityThresholds.medium) {
      return 'medium';
    } else {
      return 'light';
    }
  }, [config]);

  // ===== 高亮样式类计算 =====

  const getHighlightClass = useCallback((wordText: string, usageCount: number): string => {
    if (!config.enabled) {
      return '';
    }

    const classes: string[] = [styles.duplicateHighlight];

    // 添加主题类
    if (config.theme !== 'default') {
      classes.push(`[data-theme="${config.theme}"]`);
    }

    // 添加强度类
    const intensity = getHighlightIntensity(usageCount);
    switch (intensity) {
      case 'light':
        classes.push(styles.duplicateLight);
        break;
      case 'medium':
        classes.push(styles.duplicateMedium);
        break;
      case 'heavy':
        classes.push(styles.duplicateHeavy);
        break;
      case 'extreme':
        classes.push(styles.duplicateExtreme);
        break;
    }

    // 添加状态类
    if (state.selectedDuplicates.has(wordText)) {
      classes.push(styles.duplicateSelected);
    }

    if (state.focusedDuplicate === wordText) {
      classes.push(styles.duplicateFocused);
    }

    if (state.newDuplicates.has(wordText)) {
      classes.push(styles.duplicateNew);
    }

    if (state.removingDuplicates.has(wordText)) {
      classes.push(styles.duplicateRemoving);
    }

    // 禁用动画
    if (!config.enableAnimations) {
      classes.push(styles.duplicateDisabled);
    }

    return classes.join(' ');
  }, [config, state, getHighlightIntensity]);

  // ===== 自定义颜色应用 =====

  useEffect(() => {
    if (config.customColors) {
      const root = document.documentElement;
      
      if (config.customColors.light) {
        root.style.setProperty('--duplicate-primary', config.customColors.light);
      }
      if (config.customColors.medium) {
        root.style.setProperty('--duplicate-secondary', config.customColors.medium);
      }
      if (config.customColors.heavy) {
        root.style.setProperty('--duplicate-tertiary', config.customColors.heavy);
      }
      if (config.customColors.extreme) {
        root.style.setProperty('--duplicate-quaternary', config.customColors.extreme);
      }
    }
  }, [config.customColors]);

  // ===== Context值 =====

  const contextValue = useMemo<HighlightContextValue>(() => ({
    config,
    state,
    actions: {
      updateConfig,
      updateDuplicateDetection,
      selectDuplicate,
      deselectDuplicate,
      clearSelection,
      focusDuplicate,
      getHighlightClass,
      getHighlightIntensity,
    },
  }), [
    config,
    state,
    updateConfig,
    updateDuplicateDetection,
    selectDuplicate,
    deselectDuplicate,
    clearSelection,
    focusDuplicate,
    getHighlightClass,
    getHighlightIntensity,
  ]);

  return (
    <HighlightContext.Provider value={contextValue}>
      {children}
    </HighlightContext.Provider>
  );
};

// ===== Hook =====

export const useDuplicateHighlight = (): HighlightContextValue => {
  const context = useContext(HighlightContext);
  if (!context) {
    throw new Error('useDuplicateHighlight must be used within a DuplicateHighlightProvider');
  }
  return context;
};

// ===== 便捷组件 =====

interface HighlightedCellProps {
  children: React.ReactNode;
  wordText?: string;
  usageCount?: number;
  className?: string;
  onClick?: () => void;
  onFocus?: () => void;
  onBlur?: () => void;
}

export const HighlightedCell: React.FC<HighlightedCellProps> = ({
  children,
  wordText,
  usageCount = 0,
  className = '',
  onClick,
  onFocus,
  onBlur,
}) => {
  const { actions, state } = useDuplicateHighlight();

  const isDuplicate = wordText && state.duplicateDetection?.duplicateWords.has(wordText);
  const highlightClass = isDuplicate ? actions.getHighlightClass(wordText, usageCount) : '';

  const handleClick = useCallback(() => {
    if (isDuplicate && wordText) {
      if (state.selectedDuplicates.has(wordText)) {
        actions.deselectDuplicate(wordText);
      } else {
        actions.selectDuplicate(wordText);
      }
    }
    onClick?.();
  }, [isDuplicate, wordText, state.selectedDuplicates, actions, onClick]);

  const handleFocus = useCallback(() => {
    if (isDuplicate && wordText) {
      actions.focusDuplicate(wordText);
    }
    onFocus?.();
  }, [isDuplicate, wordText, actions, onFocus]);

  const handleBlur = useCallback(() => {
    if (isDuplicate && wordText) {
      actions.focusDuplicate(null);
    }
    onBlur?.();
  }, [isDuplicate, wordText, actions, onBlur]);

  return (
    <div
      className={`${highlightClass} ${className}`.trim()}
      onClick={handleClick}
      onFocus={handleFocus}
      onBlur={handleBlur}
      tabIndex={isDuplicate ? 0 : undefined}
      role={isDuplicate ? 'button' : undefined}
      aria-label={isDuplicate ? `重复词语: ${wordText}, 使用${usageCount}次` : undefined}
    >
      {children}
    </div>
  );
};
