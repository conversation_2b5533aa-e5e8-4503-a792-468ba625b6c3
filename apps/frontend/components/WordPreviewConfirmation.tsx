/**
 * 词语预览确认组件
 * 🎯 核心价值：提供词语预览状态的确认和取消操作界面
 * 📦 功能范围：预览显示、确认按钮、取消按钮、撤销功能
 * 🔄 架构设计：浮动式确认面板，支持键盘快捷键操作
 */

'use client';

import React, { useCallback, useEffect, useRef } from 'react';
import type { WordEntry } from '../core/word-library/WordLibraryTypes';
import type { Coordinate } from '../core/matrix/MatrixTypes';

// ===== 类型定义 =====

interface WordPreviewConfirmationProps {
  isVisible: boolean;
  word: WordEntry;
  coordinate: Coordinate;
  position: { x: number; y: number };
  onConfirm: () => void;
  onCancel: () => void;
  onUndo?: () => void;
  className?: string;
}

// ===== 主组件 =====

export const WordPreviewConfirmation: React.FC<WordPreviewConfirmationProps> = ({
  isVisible,
  word,
  coordinate,
  position,
  onConfirm,
  onCancel,
  onUndo,
  className = '',
}) => {
  const panelRef = useRef<HTMLDivElement>(null);

  // 键盘快捷键处理
  useEffect(() => {
    if (!isVisible) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'Enter':
          event.preventDefault();
          onConfirm();
          break;
        case 'Escape':
          event.preventDefault();
          onCancel();
          break;
        case 'u':
          if (event.ctrlKey && onUndo) {
            event.preventDefault();
            onUndo();
          }
          break;
        case 'y':
        case 'Y':
          event.preventDefault();
          onConfirm();
          break;
        case 'n':
        case 'N':
          event.preventDefault();
          onCancel();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isVisible, onConfirm, onCancel, onUndo]);

  // 点击外部关闭
  useEffect(() => {
    if (!isVisible) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (panelRef.current && !panelRef.current.contains(event.target as Node)) {
        onCancel();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isVisible, onCancel]);

  // 计算面板位置
  const panelStyle = React.useMemo(() => {
    if (!isVisible) return { display: 'none' };

    const panelWidth = 280;
    const panelHeight = 120;
    const padding = 20;

    let left = position.x - panelWidth / 2;
    let top = position.y - panelHeight - 10; // 在单元格上方显示

    // 防止超出边界
    if (left < padding) left = padding;
    if (left + panelWidth > window.innerWidth - padding) {
      left = window.innerWidth - panelWidth - padding;
    }
    if (top < padding) {
      top = position.y + 40; // 在单元格下方显示
    }

    return {
      position: 'fixed' as const,
      left: `${left}px`,
      top: `${top}px`,
      zIndex: 1100,
    };
  }, [position, isVisible]);

  if (!isVisible) return null;

  return (
    <div
      ref={panelRef}
      className={`
        bg-white border border-gray-300 rounded-lg shadow-lg
        ${className}
      `}
      style={panelStyle}
    >
      {/* 标题栏 */}
      <div className="px-4 py-2 border-b border-gray-200 bg-gray-50 rounded-t-lg">
        <div className="flex items-center justify-between">
          <h4 className="text-sm font-semibold text-gray-800">
            预览确认
          </h4>
          <div className="text-xs text-gray-500">
            ({coordinate.x}, {coordinate.y})
          </div>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="p-4">
        <div className="flex items-center gap-3 mb-4">
          {/* 词语颜色指示器 */}
          <div
            className="w-4 h-4 rounded-full border border-gray-300"
            style={{
              backgroundColor: getColorHex(word.category.color),
            }}
          />
          
          {/* 词语信息 */}
          <div className="flex-1">
            <div className="font-semibold text-gray-900">
              {word.text}
            </div>
            <div className="text-xs text-gray-500">
              {word.category.color} · 等级{word.category.level}
            </div>
          </div>
          
          {/* 使用次数 */}
          <div className="text-xs text-gray-400">
            使用 {word.metadata.usageCount} 次
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex gap-2">
          <button
            onClick={onConfirm}
            className="
              flex-1 px-3 py-2 bg-blue-500 text-white text-sm font-medium rounded
              hover:bg-blue-600 focus:ring-2 focus:ring-blue-300 focus:outline-none
              transition-colors duration-200
            "
          >
            确认填入 (Enter/Y)
          </button>
          
          <button
            onClick={onCancel}
            className="
              flex-1 px-3 py-2 bg-gray-500 text-white text-sm font-medium rounded
              hover:bg-gray-600 focus:ring-2 focus:ring-gray-300 focus:outline-none
              transition-colors duration-200
            "
          >
            取消 (Esc/N)
          </button>
          
          {onUndo && (
            <button
              onClick={onUndo}
              className="
                px-3 py-2 bg-orange-500 text-white text-sm font-medium rounded
                hover:bg-orange-600 focus:ring-2 focus:ring-orange-300 focus:outline-none
                transition-colors duration-200
              "
              title="撤销上一步操作"
            >
              撤销
            </button>
          )}
        </div>
      </div>

      {/* 底部提示 */}
      <div className="px-4 py-2 border-t border-gray-200 bg-gray-50 rounded-b-lg">
        <div className="text-xs text-gray-600 text-center">
          {onUndo ? 'Enter确认 | Esc取消 | Ctrl+U撤销' : 'Enter确认 | Esc取消'}
        </div>
      </div>
    </div>
  );
};

// ===== 工具函数 =====

const getColorHex = (color: string): string => {
  const colorMap: Record<string, string> = {
    red: '#ef4444',
    cyan: '#06b6d4',
    yellow: '#eab308',
    purple: '#a855f7',
    orange: '#f97316',
    green: '#22c55e',
    blue: '#3b82f6',
    pink: '#ec4899',
    black: '#374151',
  };
  
  return colorMap[color] || '#6b7280';
};

export default WordPreviewConfirmation;
