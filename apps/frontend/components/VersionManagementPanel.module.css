/**
 * 版本管理面板样式
 * 🎯 核心价值：提供美观、易用的版本管理界面样式
 * 📦 功能范围：面板布局、版本列表、操作按钮、响应式设计
 * 🔄 架构设计：基于CSS Modules的组件样式，支持主题和动画
 */

/* ===== 面板容器 ===== */

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.panel {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  width: 90vw;
  max-width: 800px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* ===== 面板头部 ===== */

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.closeButton:hover {
  background-color: #f3f4f6;
  color: #374151;
}

/* ===== 面板内容 ===== */

.content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* ===== 创建版本区域 ===== */

.createSection {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  background: #f9fafb;
}

.createSection h3 {
  margin: 0 0 16px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.createForm {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.input {
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.textarea {
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  resize: vertical;
  min-height: 60px;
  font-family: inherit;
  transition: border-color 0.2s ease;
}

.textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.createButton {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  align-self: flex-start;
}

.createButton:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
}

.createButton:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

/* ===== 导入区域 ===== */

.importSection {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}

.importSection h3 {
  margin: 0 0 16px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.fileInput {
  padding: 8px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  width: 100%;
}

/* ===== 版本列表 ===== */

.versionList h3 {
  margin: 0 0 16px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.emptyMessage {
  text-align: center;
  color: #6b7280;
  font-style: italic;
  padding: 40px 20px;
}

.versionItems {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* ===== 版本项 ===== */

.versionItem {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.versionItem:hover {
  border-color: #d1d5db;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.versionItem.active {
  border-color: #3b82f6;
  background: #eff6ff;
}

.versionHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  cursor: pointer;
  background: white;
  transition: background-color 0.2s ease;
}

.versionItem.active .versionHeader {
  background: #eff6ff;
}

.versionHeader:hover {
  background: #f9fafb;
}

.versionItem.active .versionHeader:hover {
  background: #dbeafe;
}

.versionInfo {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.versionName {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
}

.versionDate {
  font-size: 0.875rem;
  color: #6b7280;
}

.activeLabel {
  display: inline-block;
  background: #3b82f6;
  color: white;
  font-size: 0.75rem;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 12px;
  margin-top: 4px;
  align-self: flex-start;
}

.versionStats {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat {
  font-size: 0.875rem;
  color: #6b7280;
  white-space: nowrap;
}

.expandButton {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  font-size: 0.875rem;
  transition: color 0.2s ease;
}

.expandButton:hover {
  color: #374151;
}

/* ===== 版本详情 ===== */

.versionDetails {
  padding: 20px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  animation: expandIn 0.2s ease-out;
}

@keyframes expandIn {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 300px;
  }
}

.versionDescription {
  margin: 0 0 16px 0;
  color: #374151;
  font-size: 0.875rem;
  line-height: 1.5;
}

.versionMetadata {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 20px;
}

.metadataItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.metadataLabel {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.metadataValue {
  font-size: 0.875rem;
  color: #111827;
  font-weight: 600;
}

/* ===== 版本操作 ===== */

.versionActions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.actionButton {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.actionButton:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
  transform: translateY(-1px);
}

.deleteButton {
  background: #fef2f2;
  color: #dc2626;
  border-color: #fecaca;
}

.deleteButton:hover {
  background: #fee2e2;
  border-color: #fca5a5;
}

/* ===== 比较结果 ===== */

.comparisonSection {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  background: #f0f9ff;
}

.comparisonSection h3 {
  margin: 0 0 16px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.comparisonStats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
}

.statItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e0f2fe;
}

.statLabel {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
  margin-bottom: 4px;
}

.statValue {
  font-size: 1.25rem;
  color: #0369a1;
  font-weight: 700;
}

.closeComparisonButton {
  background: #0ea5e9;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.closeComparisonButton:hover {
  background: #0284c7;
  transform: translateY(-1px);
}

/* ===== 加载状态 ===== */

.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loadingMessage {
  background: white;
  padding: 20px 40px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  font-weight: 500;
  color: #374151;
}

/* ===== 响应式设计 ===== */

@media (max-width: 768px) {
  .panel {
    width: 95vw;
    max-height: 95vh;
  }

  .header {
    padding: 16px 20px;
  }

  .content {
    padding: 20px;
    gap: 20px;
  }

  .versionStats {
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
  }

  .versionActions {
    justify-content: flex-start;
  }

  .comparisonStats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .versionHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .versionStats {
    align-self: stretch;
    flex-direction: row;
    justify-content: space-between;
  }

  .versionActions {
    flex-direction: column;
  }

  .actionButton {
    width: 100%;
    text-align: center;
  }
}
