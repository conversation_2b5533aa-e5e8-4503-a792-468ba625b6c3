/**
 * 填词模式管理器
 * 🎯 核心价值：管理填词模式的激活、词语选择、预览和确认流程
 * 📦 功能范围：模式状态管理、事件处理、词语绑定、预览控制
 * 🔄 架构设计：基于状态机的填词流程管理，支持撤销和重做
 */

import { produce } from 'immer';
import { create } from 'zustand';
import type { Coordinate } from '../matrix/MatrixTypes';
import type { WordEntry } from './WordLibraryTypes';

// ===== 类型定义 =====

/** 填词模式状态 */
export type WordFillingMode = 'inactive' | 'selecting' | 'previewing' | 'confirmed';

/** 填词会话数据 */
export interface WordFillingSession {
  id: string;
  coordinate: Coordinate;
  mode: WordFillingMode;
  selectedWord?: WordEntry;
  previewWord?: WordEntry;
  startTime: number;
  lastActivity: number;
}

/** 填词历史记录 */
export interface WordFillingHistory {
  id: string;
  coordinate: Coordinate;
  word: WordEntry;
  action: 'fill' | 'remove' | 'replace';
  timestamp: number;
  previousWord?: WordEntry;
}

/** 填词管理器状态 */
interface WordFillingManagerState {
  // 当前会话
  currentSession: WordFillingSession | null;

  // 历史记录
  history: WordFillingHistory[];
  historyIndex: number;

  // 预览状态
  previewCells: Map<string, WordEntry>; // coordinate -> word

  // 配置
  config: {
    autoPreview: boolean;
    confirmationRequired: boolean;
    sessionTimeout: number; // 毫秒
  };

  // 统计
  stats: {
    totalFills: number;
    totalUndos: number;
    averageSelectionTime: number;
  };
}

/** 填词管理器操作 */
interface WordFillingManagerActions {
  // 会话管理
  startSession: (coordinate: Coordinate) => string;
  endSession: () => void;
  getSession: () => WordFillingSession | null;

  // 词语选择和预览
  selectWord: (word: WordEntry) => void;
  previewWord: (word: WordEntry) => void;
  clearPreview: () => void;

  // 确认和取消
  confirmSelection: () => boolean;
  cancelSelection: () => void;

  // 历史管理
  undo: () => boolean;
  redo: () => boolean;
  canUndo: () => boolean;
  canRedo: () => boolean;
  clearHistory: () => void;

  // 批量操作
  fillMultipleCells: (fills: Array<{ coordinate: Coordinate; word: WordEntry }>) => void;
  clearAllPreviews: () => void;

  // 配置管理
  updateConfig: (config: Partial<WordFillingManagerState['config']>) => void;

  // 工具方法
  isCoordinateInPreview: (coordinate: Coordinate) => boolean;
  getPreviewWord: (coordinate: Coordinate) => WordEntry | undefined;
  getFillingStats: () => WordFillingManagerState['stats'];
}

type WordFillingManager = WordFillingManagerState & WordFillingManagerActions;

// ===== 工具函数 =====

const coordinateToKey = (coord: Coordinate): string => `${coord.x},${coord.y}`;

const generateSessionId = (): string => `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

const generateHistoryId = (): string => `history_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

// ===== Store 实现 =====

export const useWordFillingManager = create<WordFillingManager>()((set, get) => ({
  // 初始状态
  currentSession: null,
  history: [],
  historyIndex: -1,
  previewCells: new Map(),
  config: {
    autoPreview: true,
    confirmationRequired: false,
    sessionTimeout: 30000, // 30秒
  },
  stats: {
    totalFills: 0,
    totalUndos: 0,
    averageSelectionTime: 0,
  },

  // 会话管理
  startSession: (coordinate: Coordinate) => {
    const sessionId = generateSessionId();

    set(produce((state: WordFillingManagerState) => {
      // 结束当前会话
      if (state.currentSession) {
        state.currentSession.mode = 'inactive';
      }

      // 创建新会话
      state.currentSession = {
        id: sessionId,
        coordinate,
        mode: 'selecting',
        startTime: Date.now(),
        lastActivity: Date.now(),
      };
    }));

    return sessionId;
  },

  endSession: () => {
    set(produce((state: WordFillingManagerState) => {
      if (state.currentSession) {
        state.currentSession.mode = 'inactive';
        state.currentSession = null;
      }
    }));
  },

  getSession: () => {
    return get().currentSession;
  },

  // 词语选择和预览
  selectWord: (word: WordEntry) => {
    set(produce((state: WordFillingManagerState) => {
      if (state.currentSession) {
        state.currentSession.selectedWord = word;
        state.currentSession.lastActivity = Date.now();

        // 如果启用自动预览
        if (state.config.autoPreview) {
          state.currentSession.previewWord = word;
          state.currentSession.mode = 'previewing';

          const key = coordinateToKey(state.currentSession.coordinate);
          state.previewCells.set(key, word);
        }
      }
    }));
  },

  previewWord: (word: WordEntry) => {
    set(produce((state: WordFillingManagerState) => {
      if (state.currentSession) {
        state.currentSession.previewWord = word;
        state.currentSession.mode = 'previewing';
        state.currentSession.lastActivity = Date.now();

        const key = coordinateToKey(state.currentSession.coordinate);
        state.previewCells.set(key, word);
      }
    }));
  },

  clearPreview: () => {
    set(produce((state: WordFillingManagerState) => {
      if (state.currentSession) {
        const key = coordinateToKey(state.currentSession.coordinate);
        state.previewCells.delete(key);
        state.currentSession.previewWord = undefined;

        if (state.currentSession.selectedWord) {
          state.currentSession.mode = 'selecting';
        }
      }
    }));
  },

  // 确认和取消
  confirmSelection: () => {
    const state = get();
    if (!state.currentSession || !state.currentSession.selectedWord) {
      return false;
    }

    set(produce((draft: WordFillingManagerState) => {
      const session = draft.currentSession!;
      const word = session.selectedWord!;

      // 添加到历史记录
      const historyEntry: WordFillingHistory = {
        id: generateHistoryId(),
        coordinate: session.coordinate,
        word,
        action: 'fill',
        timestamp: Date.now(),
      };

      // 清理重做历史
      draft.history = draft.history.slice(0, draft.historyIndex + 1);
      draft.history.push(historyEntry);
      draft.historyIndex = draft.history.length - 1;

      // 更新统计
      draft.stats.totalFills++;
      const selectionTime = Date.now() - session.startTime;
      draft.stats.averageSelectionTime =
        (draft.stats.averageSelectionTime * (draft.stats.totalFills - 1) + selectionTime) / draft.stats.totalFills;

      // 确认状态
      session.mode = 'confirmed';

      // 清理预览
      const key = coordinateToKey(session.coordinate);
      draft.previewCells.delete(key);
    }));

    return true;
  },

  cancelSelection: () => {
    set(produce((state: WordFillingManagerState) => {
      if (state.currentSession) {
        const key = coordinateToKey(state.currentSession.coordinate);
        state.previewCells.delete(key);
        state.currentSession = null;
      }
    }));
  },

  // 历史管理
  undo: () => {
    const state = get();
    if (state.historyIndex < 0) return false;

    set(produce((draft: WordFillingManagerState) => {
      draft.historyIndex--;
      draft.stats.totalUndos++;
    }));

    return true;
  },

  redo: () => {
    const state = get();
    if (state.historyIndex >= state.history.length - 1) return false;

    set(produce((draft: WordFillingManagerState) => {
      draft.historyIndex++;
    }));

    return true;
  },

  canUndo: () => {
    return get().historyIndex >= 0;
  },

  canRedo: () => {
    const state = get();
    return state.historyIndex < state.history.length - 1;
  },

  clearHistory: () => {
    set(produce((state: WordFillingManagerState) => {
      state.history = [];
      state.historyIndex = -1;
    }));
  },

  // 批量操作
  fillMultipleCells: (fills: Array<{ coordinate: Coordinate; word: WordEntry }>) => {
    set(produce((state: WordFillingManagerState) => {
      fills.forEach(({ coordinate, word }) => {
        const historyEntry: WordFillingHistory = {
          id: generateHistoryId(),
          coordinate,
          word,
          action: 'fill',
          timestamp: Date.now(),
        };

        state.history.push(historyEntry);
        state.stats.totalFills++;
      });

      state.historyIndex = state.history.length - 1;
    }));
  },

  clearAllPreviews: () => {
    set(produce((state: WordFillingManagerState) => {
      state.previewCells.clear();
      if (state.currentSession) {
        state.currentSession.previewWord = undefined;
        if (state.currentSession.selectedWord) {
          state.currentSession.mode = 'selecting';
        }
      }
    }));
  },

  // 配置管理
  updateConfig: (configUpdate) => {
    set(produce((state: WordFillingManagerState) => {
      Object.assign(state.config, configUpdate);
    }));
  },

  // 工具方法
  isCoordinateInPreview: (coordinate: Coordinate) => {
    const key = coordinateToKey(coordinate);
    return get().previewCells.has(key);
  },

  getPreviewWord: (coordinate: Coordinate) => {
    const key = coordinateToKey(coordinate);
    return get().previewCells.get(key);
  },

  getFillingStats: () => {
    return get().stats;
  },
}));

// ===== 导出工具函数 =====

export { coordinateToKey, generateHistoryId, generateSessionId };

