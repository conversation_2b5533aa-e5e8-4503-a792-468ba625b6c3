/**
 * 词库系统核心类型定义
 * 🎯 核心价值：统一的词库类型系统，支持词语管理、绑定关系、版本控制
 * 📦 功能范围：词语数据、绑定关系、版本管理、重复检测、导航状态
 * 🔄 架构设计：基于数据驱动的类型设计，支持高性能计算属性
 */

import type { BasicColorType } from '../matrix/MatrixTypes';

// ===== 基础类型 =====

/** 颜色等级 */
export type ColorLevel = 1 | 2 | 3 | 4;

/** 词语唯一标识符 */
export type WordId = string;

/** 单元格键格式 "x,y" */
export type CellKey = string;

// ===== 词语条目数据结构 =====

/** 词语分类 */
export interface WordCategory {
  color: BasicColorType;  // 所属颜色类别 (红、青、黄、紫、橙、绿、蓝、粉)
  level: ColorLevel;      // 所属等级 (1-4)
}

/** 词语元数据 */
export interface WordMetadata {
  createdAt: Date;        // 创建时间
  updatedAt: Date;        // 更新时间
  usageCount: number;     // 使用次数统计
  lastUsed?: Date;        // 最后使用时间
}

/** 词语验证信息 */
export interface WordValidation {
  isValid: boolean;           // 是否为有效中文词语
  validationErrors?: string[]; // 验证错误信息
}

/** 词语条目 */
export interface WordEntry {
  id: WordId;                 // 唯一标识符 (UUID)
  text: string;               // 中文词语文本
  category: WordCategory;     // 分类信息
  metadata: WordMetadata;     // 元数据
  validation: WordValidation; // 验证信息
}

// ===== 词库数据结构 =====

/** 词库统计信息 */
export interface WordLibraryStatistics {
  wordsByColor: Record<BasicColorType, number>;
  wordsByLevel: Record<ColorLevel, number>;
  duplicateWords: string[];
}

/** 词库元数据 */
export interface WordLibraryMetadata {
  totalWords: number;
  lastUpdated: Date;
  version: string;
}

/** 词库数据 */
export interface WordLibraryData {
  categories: Record<BasicColorType, Record<ColorLevel, WordEntry[]>>;
  metadata: WordLibraryMetadata;
  statistics: WordLibraryStatistics;
}

// ===== 单元格词语绑定 =====

/** 单元格词语绑定关系 */
export interface CellWordBinding {
  cellKey: CellKey;           // "x,y" 格式的单元格键
  wordId: WordId;             // 绑定的词语ID
  bindingTime: Date;          // 绑定时间
  isTemporary?: boolean;      // 是否为临时绑定(预览状态)
}

/** 绑定关系映射 */
export type CellWordBindings = Map<CellKey, CellWordBinding>;

// ===== 版本管理 =====

/** 版本元数据 */
export interface WordVersionMetadata {
  totalBindings: number;      // 绑定总数
  coverageRate: number;       // 覆盖率
  wordCount: number;          // 使用的词语总数
  duplicateCount: number;     // 重复词语数量
}

/** 词语版本 */
export interface WordVersion {
  id: string;                 // 版本唯一标识符
  name: string;               // 版本名称
  description?: string;       // 版本描述
  createdAt: Date;           // 创建时间
  bindings: CellWordBinding[]; // 该版本的所有绑定关系
  metadata: WordVersionMetadata; // 版本元数据
  checksum: string;          // 数据校验和
}

// ===== 导航状态 =====

/** 过滤条件 */
export interface FilterCriteria {
  color?: BasicColorType;
  level?: ColorLevel;
  searchText?: string;
}

/** 导航状态 */
export interface NavigationState {
  isActive: boolean;            // 是否处于选择模式
  targetCell: { x: number; y: number } | null;
  availableWords: WordEntry[];  // 当前可选词语列表
  selectedIndex: number;        // 当前选择索引
  lastSelectedIndex: Map<string, number>; // 每个分类的上次选择位置
  previewMode: boolean;         // 是否显示预览
  filterCriteria: FilterCriteria; // 过滤条件
}

// ===== 重复检测 =====

/** 重复词语组 */
export interface DuplicateGroup {
  wordText: string;                              // 词语文本
  positions: Array<{ x: number; y: number }>;   // 使用位置列表
  usageCount: number;                            // 使用次数
  wordIds: WordId[];                             // 相关词语ID列表
}

/** 重复检测统计 */
export interface DuplicateStatistics {
  totalDuplicates: number;                       // 重复词语总数
  duplicateRate: number;                         // 重复率
  maxUsageCount: number;                         // 最大使用次数
}

/** 重复检测结果 */
export interface DuplicateDetection {
  duplicateGroups: Map<string, DuplicateGroup>;  // 按词语文本分组
  duplicateWords: Set<string>;                   // 重复词语集合
  statistics: DuplicateStatistics;               // 统计信息
  lastDetectionTime: Date;                       // 最后检测时间
}

// ===== 工具函数类型 =====

/** 词语创建参数 */
export type CreateWordParams = Omit<WordEntry, 'id' | 'metadata'>;

/** 词语更新参数 */
export type UpdateWordParams = Partial<Omit<WordEntry, 'id'>>;

/** 批量操作结果 */
export interface BatchOperationResult {
  success: boolean;
  processed: number;
  errors: string[];
}

// ===== 验证函数 =====

/** 中文字符验证正则表达式 */
export const CHINESE_TEXT_REGEX = /^[\u4e00-\u9fff]+$/;

/** 验证中文词语 */
export const validateChineseText = (text: string): boolean => {
  return CHINESE_TEXT_REGEX.test(text) && text.length > 0 && text.length <= 10;
};

/** 生成词语ID */
export const generateWordId = (): WordId => {
  return `word_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/** 生成单元格键 */
export const generateCellKey = (x: number, y: number): CellKey => `${x},${y}`;

/** 解析单元格键 */
export const parseCellKey = (cellKey: CellKey): { x: number; y: number } => {
  const [x, y] = cellKey.split(',').map(Number);
  return { x, y };
};

// ===== 默认值 =====

/** 创建默认词语分类 */
export const createDefaultWordCategory = (color: BasicColorType, level: ColorLevel): WordCategory => ({
  color,
  level,
});

/** 创建默认词语元数据 */
export const createDefaultWordMetadata = (): WordMetadata => ({
  createdAt: new Date(),
  updatedAt: new Date(),
  usageCount: 0,
});

/** 创建默认词语验证信息 */
export const createDefaultWordValidation = (text: string): WordValidation => ({
  isValid: validateChineseText(text),
  validationErrors: validateChineseText(text) ? undefined : ['词语必须包含有效的中文字符'],
});

/** 创建默认导航状态 */
export const createDefaultNavigationState = (): NavigationState => ({
  isActive: false,
  targetCell: null,
  availableWords: [],
  selectedIndex: 0,
  lastSelectedIndex: new Map(),
  previewMode: false,
  filterCriteria: {},
});

/** 创建默认重复检测结果 */
export const createDefaultDuplicateDetection = (): DuplicateDetection => ({
  duplicateGroups: new Map(),
  duplicateWords: new Set(),
  statistics: {
    totalDuplicates: 0,
    duplicateRate: 0,
    maxUsageCount: 0,
  },
  lastDetectionTime: new Date(),
});

// ===== 类型守卫 =====

/** 检查是否为有效的词语条目 */
export const isValidWordEntry = (obj: any): obj is WordEntry => {
  return (
    obj &&
    typeof obj.id === 'string' &&
    typeof obj.text === 'string' &&
    obj.category &&
    obj.metadata &&
    obj.validation
  );
};

/** 检查是否为有效的绑定关系 */
export const isValidCellWordBinding = (obj: any): obj is CellWordBinding => {
  return (
    obj &&
    typeof obj.cellKey === 'string' &&
    typeof obj.wordId === 'string' &&
    obj.bindingTime instanceof Date
  );
};
