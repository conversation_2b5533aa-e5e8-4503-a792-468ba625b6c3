/**
 * 词语重复检测服务
 * 🎯 核心价值：高性能的重复检测算法，支持实时检测和批量检测
 * 📦 功能范围：重复检测、性能优化、统计分析、缓存管理
 * 🔄 架构设计：基于增量更新的高效算法，支持大规模数据处理
 */

import type {
  CellWordBinding,
  CellWordBindings,
  DuplicateDetection,
  DuplicateGroup,
  DuplicateStatistics,
  WordEntry,
  WordId,
  WordLibraryData,
} from './WordLibraryTypes';
import {
  createDefaultDuplicateDetection,
  parseCellKey,
} from './WordLibraryTypes';

// ===== 检测配置 =====

/** 重复检测配置 */
export interface DuplicateDetectionConfig {
  /** 是否启用实时检测 */
  enableRealTimeDetection: boolean;
  /** 是否启用缓存 */
  enableCache: boolean;
  /** 批量检测的阈值 */
  batchDetectionThreshold: number;
  /** 缓存过期时间（毫秒） */
  cacheExpirationTime: number;
  /** 是否忽略临时绑定 */
  ignoreTemporaryBindings: boolean;
}

/** 默认检测配置 */
export const DEFAULT_DETECTION_CONFIG: DuplicateDetectionConfig = {
  enableRealTimeDetection: true,
  enableCache: true,
  batchDetectionThreshold: 1000,
  cacheExpirationTime: 5 * 60 * 1000, // 5分钟
  ignoreTemporaryBindings: true,
};

// ===== 缓存管理 =====

/** 检测缓存项 */
interface DetectionCacheItem {
  result: DuplicateDetection;
  timestamp: number;
  bindingsHash: string;
}

/** 增量更新信息 */
interface IncrementalUpdate {
  addedBindings: CellWordBinding[];
  removedBindings: string[]; // cellKey[]
  modifiedBindings: CellWordBinding[];
}

// ===== 重复检测服务 =====

export class DuplicateDetectionService {
  private config: DuplicateDetectionConfig;
  private cache: Map<string, DetectionCacheItem> = new Map();
  private lastDetectionResult: DuplicateDetection;
  private lastBindingsSnapshot: Map<string, CellWordBinding> = new Map();

  constructor(config: Partial<DuplicateDetectionConfig> = {}) {
    this.config = { ...DEFAULT_DETECTION_CONFIG, ...config };
    this.lastDetectionResult = createDefaultDuplicateDetection();
  }

  /**
   * 主要检测方法：根据配置选择最优检测策略
   */
  public detectDuplicates(
    cellWordBindings: CellWordBindings,
    wordLibrary: WordLibraryData,
    forceFullDetection = false
  ): DuplicateDetection {
    const bindingsHash = this.generateBindingsHash(cellWordBindings);

    // 检查缓存
    if (this.config.enableCache && !forceFullDetection) {
      const cached = this.getCachedResult(bindingsHash);
      if (cached) {
        return cached;
      }
    }

    // 选择检测策略
    const bindingsSize = cellWordBindings.size;
    let result: DuplicateDetection;

    if (
      this.config.enableRealTimeDetection &&
      !forceFullDetection &&
      bindingsSize < this.config.batchDetectionThreshold
    ) {
      // 增量检测
      result = this.performIncrementalDetection(cellWordBindings, wordLibrary);
    } else {
      // 全量检测
      result = this.performFullDetection(cellWordBindings, wordLibrary);
    }

    // 更新缓存
    if (this.config.enableCache) {
      this.updateCache(bindingsHash, result);
    }

    // 更新快照
    this.updateBindingsSnapshot(cellWordBindings);
    this.lastDetectionResult = result;

    return result;
  }

  /**
   * 全量重复检测
   */
  private performFullDetection(
    cellWordBindings: CellWordBindings,
    wordLibrary: WordLibraryData
  ): DuplicateDetection {
    const duplicateGroups = new Map<string, DuplicateGroup>();
    const duplicateWords = new Set<string>();
    const wordTextCount = new Map<string, number>();
    const wordTextToBindings = new Map<string, CellWordBinding[]>();

    // 第一遍扫描：统计词语使用次数和收集绑定信息
    cellWordBindings.forEach((binding) => {
      if (this.config.ignoreTemporaryBindings && binding.isTemporary) {
        return;
      }

      const word = this.findWordById(wordLibrary, binding.wordId);
      if (!word) return;

      const wordText = word.text;
      
      // 统计使用次数
      const count = wordTextCount.get(wordText) || 0;
      wordTextCount.set(wordText, count + 1);

      // 收集绑定信息
      const bindings = wordTextToBindings.get(wordText) || [];
      bindings.push(binding);
      wordTextToBindings.set(wordText, bindings);
    });

    // 第二遍扫描：识别重复词语并构建重复组
    wordTextCount.forEach((count, wordText) => {
      if (count > 1) {
        duplicateWords.add(wordText);

        const bindings = wordTextToBindings.get(wordText) || [];
        const positions = bindings.map(binding => {
          const { x, y } = parseCellKey(binding.cellKey);
          return { x, y };
        });
        const wordIds = bindings.map(binding => binding.wordId);

        duplicateGroups.set(wordText, {
          wordText,
          positions,
          usageCount: count,
          wordIds,
        });
      }
    });

    // 计算统计信息
    const statistics = this.calculateStatistics(wordTextCount, duplicateWords);

    return {
      duplicateGroups,
      duplicateWords,
      statistics,
      lastDetectionTime: new Date(),
    };
  }

  /**
   * 增量重复检测
   */
  private performIncrementalDetection(
    cellWordBindings: CellWordBindings,
    wordLibrary: WordLibraryData
  ): DuplicateDetection {
    const incrementalUpdate = this.calculateIncrementalUpdate(cellWordBindings);
    
    // 如果变化太大，回退到全量检测
    const totalChanges = 
      incrementalUpdate.addedBindings.length +
      incrementalUpdate.removedBindings.length +
      incrementalUpdate.modifiedBindings.length;

    if (totalChanges > this.config.batchDetectionThreshold * 0.1) {
      return this.performFullDetection(cellWordBindings, wordLibrary);
    }

    // 基于上次结果进行增量更新
    const result = this.cloneDetectionResult(this.lastDetectionResult);
    
    // 处理移除的绑定
    this.processRemovedBindings(result, incrementalUpdate.removedBindings, wordLibrary);
    
    // 处理新增的绑定
    this.processAddedBindings(result, incrementalUpdate.addedBindings, wordLibrary);
    
    // 处理修改的绑定
    this.processModifiedBindings(result, incrementalUpdate.modifiedBindings, wordLibrary);

    // 重新计算统计信息
    result.statistics = this.calculateStatisticsFromGroups(result.duplicateGroups);
    result.lastDetectionTime = new Date();

    return result;
  }

  /**
   * 计算增量更新信息
   */
  private calculateIncrementalUpdate(cellWordBindings: CellWordBindings): IncrementalUpdate {
    const addedBindings: CellWordBinding[] = [];
    const removedBindings: string[] = [];
    const modifiedBindings: CellWordBinding[] = [];

    // 检查新增和修改的绑定
    cellWordBindings.forEach((binding, cellKey) => {
      const lastBinding = this.lastBindingsSnapshot.get(cellKey);
      
      if (!lastBinding) {
        // 新增绑定
        addedBindings.push(binding);
      } else if (
        lastBinding.wordId !== binding.wordId ||
        lastBinding.isTemporary !== binding.isTemporary
      ) {
        // 修改的绑定
        modifiedBindings.push(binding);
      }
    });

    // 检查移除的绑定
    this.lastBindingsSnapshot.forEach((_, cellKey) => {
      if (!cellWordBindings.has(cellKey)) {
        removedBindings.push(cellKey);
      }
    });

    return { addedBindings, removedBindings, modifiedBindings };
  }

  /**
   * 处理移除的绑定
   */
  private processRemovedBindings(
    result: DuplicateDetection,
    removedBindings: string[],
    wordLibrary: WordLibraryData
  ): void {
    removedBindings.forEach(cellKey => {
      const lastBinding = this.lastBindingsSnapshot.get(cellKey);
      if (!lastBinding) return;

      const word = this.findWordById(wordLibrary, lastBinding.wordId);
      if (!word) return;

      const wordText = word.text;
      const group = result.duplicateGroups.get(wordText);
      
      if (group) {
        // 移除位置
        const { x, y } = parseCellKey(cellKey);
        group.positions = group.positions.filter(pos => pos.x !== x || pos.y !== y);
        group.wordIds = group.wordIds.filter(id => id !== lastBinding.wordId);
        group.usageCount = group.positions.length;

        if (group.usageCount <= 1) {
          // 不再重复，移除组
          result.duplicateGroups.delete(wordText);
          result.duplicateWords.delete(wordText);
        }
      }
    });
  }

  /**
   * 处理新增的绑定
   */
  private processAddedBindings(
    result: DuplicateDetection,
    addedBindings: CellWordBinding[],
    wordLibrary: WordLibraryData
  ): void {
    addedBindings.forEach(binding => {
      if (this.config.ignoreTemporaryBindings && binding.isTemporary) {
        return;
      }

      const word = this.findWordById(wordLibrary, binding.wordId);
      if (!word) return;

      const wordText = word.text;
      const { x, y } = parseCellKey(binding.cellKey);
      
      let group = result.duplicateGroups.get(wordText);
      
      if (group) {
        // 已存在重复组，添加新位置
        group.positions.push({ x, y });
        group.wordIds.push(binding.wordId);
        group.usageCount = group.positions.length;
      } else {
        // 检查是否形成新的重复
        const existingUsage = this.countWordUsageInResult(result, wordText);
        if (existingUsage >= 1) {
          // 形成新的重复组
          result.duplicateWords.add(wordText);
          result.duplicateGroups.set(wordText, {
            wordText,
            positions: [{ x, y }],
            usageCount: existingUsage + 1,
            wordIds: [binding.wordId],
          });
        }
      }
    });
  }

  /**
   * 处理修改的绑定
   */
  private processModifiedBindings(
    result: DuplicateDetection,
    modifiedBindings: CellWordBinding[],
    wordLibrary: WordLibraryData
  ): void {
    // 对于修改的绑定，先移除旧的，再添加新的
    modifiedBindings.forEach(binding => {
      const cellKey = binding.cellKey;
      this.processRemovedBindings(result, [cellKey], wordLibrary);
      this.processAddedBindings(result, [binding], wordLibrary);
    });
  }

  /**
   * 计算统计信息
   */
  private calculateStatistics(
    wordTextCount: Map<string, number>,
    duplicateWords: Set<string>
  ): DuplicateStatistics {
    const totalWords = wordTextCount.size;
    const totalDuplicates = duplicateWords.size;
    const maxUsageCount = Math.max(...Array.from(wordTextCount.values()), 0);

    return {
      totalDuplicates,
      duplicateRate: totalWords > 0 ? totalDuplicates / totalWords : 0,
      maxUsageCount,
    };
  }

  /**
   * 从重复组计算统计信息
   */
  private calculateStatisticsFromGroups(
    duplicateGroups: Map<string, DuplicateGroup>
  ): DuplicateStatistics {
    const totalDuplicates = duplicateGroups.size;
    const maxUsageCount = Math.max(
      ...Array.from(duplicateGroups.values()).map(group => group.usageCount),
      0
    );

    return {
      totalDuplicates,
      duplicateRate: 0, // 需要总词语数才能计算，这里暂时设为0
      maxUsageCount,
    };
  }

  /**
   * 生成绑定关系的哈希值
   */
  private generateBindingsHash(cellWordBindings: CellWordBindings): string {
    const entries = Array.from(cellWordBindings.entries())
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([cellKey, binding]) => `${cellKey}:${binding.wordId}:${binding.isTemporary}`);
    
    return entries.join('|');
  }

  /**
   * 获取缓存结果
   */
  private getCachedResult(bindingsHash: string): DuplicateDetection | null {
    const cached = this.cache.get(bindingsHash);
    
    if (!cached) return null;
    
    const now = Date.now();
    if (now - cached.timestamp > this.config.cacheExpirationTime) {
      this.cache.delete(bindingsHash);
      return null;
    }
    
    return cached.result;
  }

  /**
   * 更新缓存
   */
  private updateCache(bindingsHash: string, result: DuplicateDetection): void {
    this.cache.set(bindingsHash, {
      result: this.cloneDetectionResult(result),
      timestamp: Date.now(),
      bindingsHash,
    });

    // 清理过期缓存
    this.cleanupExpiredCache();
  }

  /**
   * 清理过期缓存
   */
  private cleanupExpiredCache(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    this.cache.forEach((item, key) => {
      if (now - item.timestamp > this.config.cacheExpirationTime) {
        expiredKeys.push(key);
      }
    });

    expiredKeys.forEach(key => this.cache.delete(key));
  }

  /**
   * 更新绑定快照
   */
  private updateBindingsSnapshot(cellWordBindings: CellWordBindings): void {
    this.lastBindingsSnapshot.clear();
    cellWordBindings.forEach((binding, cellKey) => {
      this.lastBindingsSnapshot.set(cellKey, { ...binding });
    });
  }

  /**
   * 克隆检测结果
   */
  private cloneDetectionResult(result: DuplicateDetection): DuplicateDetection {
    return {
      duplicateGroups: new Map(result.duplicateGroups),
      duplicateWords: new Set(result.duplicateWords),
      statistics: { ...result.statistics },
      lastDetectionTime: new Date(result.lastDetectionTime),
    };
  }

  /**
   * 在词库中查找词语
   */
  private findWordById(wordLibrary: WordLibraryData, wordId: WordId): WordEntry | undefined {
    for (const colorCategories of Object.values(wordLibrary.categories)) {
      for (const levelWords of Object.values(colorCategories)) {
        const word = levelWords.find(w => w.id === wordId);
        if (word) return word;
      }
    }
    return undefined;
  }

  /**
   * 统计词语在结果中的使用次数
   */
  private countWordUsageInResult(result: DuplicateDetection, wordText: string): number {
    const group = result.duplicateGroups.get(wordText);
    return group ? group.usageCount : 0;
  }

  /**
   * 清理缓存
   */
  public clearCache(): void {
    this.cache.clear();
  }

  /**
   * 获取缓存统计信息
   */
  public getCacheStats(): { size: number; hitRate: number } {
    // 这里可以添加命中率统计逻辑
    return {
      size: this.cache.size,
      hitRate: 0, // 需要实现命中率统计
    };
  }
}
