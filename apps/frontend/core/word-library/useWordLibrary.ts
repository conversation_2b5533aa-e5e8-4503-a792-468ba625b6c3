/**
 * 词库管理React Hook
 * 🎯 核心价值：提供简化的词库操作接口，封装复杂的状态管理逻辑
 * 📦 功能范围：词库CRUD、搜索过滤、状态管理、错误处理
 * 🔄 架构设计：基于自定义Hook的组合式API，支持响应式更新
 */

import { useCallback, useMemo, useState } from 'react';
import type { BasicColorType } from '../matrix/MatrixTypes';
import { WordLibraryServices } from './WordLibraryService';
import { useWordLibraryStore } from './WordLibraryStore';
import type {
  BatchOperationResult,
  CreateWordParams,
  FilterCriteria,
  UpdateWordParams,
  WordCategory,
  WordEntry,
  WordId,
  WordLibraryData,
} from './WordLibraryTypes';

// ===== Hook返回类型 =====

interface UseWordLibraryReturn {
  // 状态数据
  wordLibrary: WordLibraryData;
  isLoading: boolean;
  isDirty: boolean;

  // 词语管理
  addWord: (params: CreateWordParams) => Promise<{ success: boolean; wordId?: WordId; error?: string }>;
  updateWord: (wordId: WordId, updates: UpdateWordParams) => Promise<{ success: boolean; error?: string }>;
  removeWord: (wordId: WordId) => Promise<{ success: boolean; error?: string }>;

  // 搜索和过滤
  searchWords: (criteria: FilterCriteria) => WordEntry[];
  getWordsByCategory: (color: BasicColorType, level: number) => WordEntry[];
  getAllWords: () => WordEntry[];
  getSuggestions: (partialText: string) => WordEntry[];

  // 批量操作
  batchAddWords: (words: Array<CreateWordParams & { category: WordCategory }>) => Promise<BatchOperationResult>;
  batchRemoveWords: (wordIds: WordId[]) => Promise<BatchOperationResult>;

  // 统计信息
  getStatistics: () => ReturnType<typeof WordLibraryServices.stats.getStatistics>;
  getCategoryCoverage: () => number;

  // 导入导出
  exportToCsv: () => string;
  importFromCsv: (csvData: string) => Promise<BatchOperationResult>;

  // 验证
  validateWord: (text: string, excludeWordId?: WordId) => { isValid: boolean; errors: string[] };

  // 单元格绑定
  bindWordToCell: (wordId: WordId, coordinate: { x: number; y: number }) => Promise<{ success: boolean; error?: string }>;
  unbindWordFromCell: (coordinate: { x: number; y: number }) => Promise<{ success: boolean; word?: WordEntry; error?: string }>;
}

// ===== 主Hook实现 =====

export const useWordLibrary = (): UseWordLibraryReturn => {
  const store = useWordLibraryStore();
  const [isProcessing, setIsProcessing] = useState(false);

  // ===== 词语管理操作 =====

  const addWord = useCallback(async (params: CreateWordParams) => {
    try {
      setIsProcessing(true);

      // 验证词语
      const validation = WordLibraryServices.validation.validateWord(
        params.text,
        store.wordLibrary
      );

      if (!validation.isValid) {
        return {
          success: false,
          error: validation.errors.join(', '),
        };
      }

      const wordId = await store.addWord(params);
      return {
        success: true,
        wordId,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '添加词语失败',
      };
    } finally {
      setIsProcessing(false);
    }
  }, [store]);

  const updateWord = useCallback(async (wordId: WordId, updates: UpdateWordParams) => {
    try {
      setIsProcessing(true);

      // 如果更新文本，需要验证
      if (updates.text) {
        const validation = WordLibraryServices.validation.validateWord(
          updates.text,
          store.wordLibrary,
          wordId
        );

        if (!validation.isValid) {
          return {
            success: false,
            error: validation.errors.join(', '),
          };
        }
      }

      await store.updateWord(wordId, updates);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '更新词语失败',
      };
    } finally {
      setIsProcessing(false);
    }
  }, [store]);

  const removeWord = useCallback(async (wordId: WordId) => {
    try {
      setIsProcessing(true);
      await store.removeWord(wordId);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '删除词语失败',
      };
    } finally {
      setIsProcessing(false);
    }
  }, [store]);

  // ===== 搜索和过滤 =====

  const searchWords = useCallback((criteria: FilterCriteria): WordEntry[] => {
    return WordLibraryServices.search.searchWords(store.wordLibrary, criteria);
  }, [store.wordLibrary]);

  const getWordsByCategory = useCallback((color: BasicColorType, level: number): WordEntry[] => {
    return store.getWordsByCategory(color, level as any);
  }, [store]);

  const getAllWords = useCallback((): WordEntry[] => {
    return store.getAllWords();
  }, [store]);

  const getSuggestions = useCallback((partialText: string): WordEntry[] => {
    return WordLibraryServices.search.getSuggestions(store.wordLibrary, partialText);
  }, [store.wordLibrary]);

  // ===== 批量操作 =====

  const batchAddWords = useCallback(async (
    words: Array<CreateWordParams & { category: WordCategory }>
  ): Promise<BatchOperationResult> => {
    setIsProcessing(true);
    try {
      return await WordLibraryServices.batch.batchAddWords(
        words,
        store.wordLibrary,
        store.addWord
      );
    } finally {
      setIsProcessing(false);
    }
  }, [store]);

  const batchRemoveWords = useCallback(async (wordIds: WordId[]): Promise<BatchOperationResult> => {
    setIsProcessing(true);
    try {
      return await WordLibraryServices.batch.batchRemoveWords(wordIds, store.removeWord);
    } finally {
      setIsProcessing(false);
    }
  }, [store]);

  // ===== 统计信息 =====

  const getStatistics = useCallback(() => {
    return WordLibraryServices.stats.getStatistics(store.wordLibrary);
  }, [store.wordLibrary]);

  const getCategoryCoverage = useCallback(() => {
    return WordLibraryServices.stats.getCategoryCoverage(store.wordLibrary);
  }, [store.wordLibrary]);

  // ===== 导入导出 =====

  const exportToCsv = useCallback(() => {
    return WordLibraryServices.batch.exportToCsv(store.wordLibrary);
  }, [store.wordLibrary]);

  const importFromCsv = useCallback(async (csvData: string): Promise<BatchOperationResult> => {
    setIsProcessing(true);
    try {
      const words = WordLibraryServices.batch.parseImportData(csvData);
      return await batchAddWords(words);
    } finally {
      setIsProcessing(false);
    }
  }, [batchAddWords]);

  // ===== 验证 =====

  const validateWord = useCallback((text: string, excludeWordId?: WordId) => {
    return WordLibraryServices.validation.validateWord(
      text,
      store.wordLibrary,
      excludeWordId
    );
  }, [store.wordLibrary]);

  // ===== 单元格绑定 =====

  const bindWordToCell = useCallback(async (wordId: WordId, coordinate: { x: number; y: number }) => {
    try {
      setIsProcessing(true);
      await store.bindWordToCell(coordinate.x, coordinate.y, wordId);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '绑定词语失败',
      };
    } finally {
      setIsProcessing(false);
    }
  }, [store]);

  const unbindWordFromCell = useCallback(async (coordinate: { x: number; y: number }) => {
    try {
      setIsProcessing(true);

      // 获取当前绑定的词语信息
      const binding = store.getCellBinding(coordinate.x, coordinate.y);
      let word: WordEntry | undefined;

      if (binding) {
        // 查找词语详情
        const allWords = store.getAllWords();
        word = allWords.find(w => w.id === binding.wordId);
      }

      await store.unbindWordFromCell(coordinate.x, coordinate.y);
      return { success: true, word };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '解绑词语失败',
      };
    } finally {
      setIsProcessing(false);
    }
  }, [store]);

  // ===== 返回接口 =====

  return {
    // 状态数据
    wordLibrary: store.wordLibrary,
    isLoading: store.isLoading || isProcessing,
    isDirty: store.isDirty,

    // 词语管理
    addWord,
    updateWord,
    removeWord,

    // 搜索和过滤
    searchWords,
    getWordsByCategory,
    getAllWords,
    getSuggestions,

    // 批量操作
    batchAddWords,
    batchRemoveWords,

    // 统计信息
    getStatistics,
    getCategoryCoverage,

    // 导入导出
    exportToCsv,
    importFromCsv,

    // 验证
    validateWord,

    // 单元格绑定
    bindWordToCell,
    unbindWordFromCell,
  };
};

// ===== 特定功能的子Hook =====

/**
 * 词语搜索Hook
 */
export const useWordSearch = (initialCriteria?: FilterCriteria) => {
  const { searchWords, getSuggestions } = useWordLibrary();
  const [criteria, setCriteria] = useState<FilterCriteria>(initialCriteria || {});
  const [searchText, setSearchText] = useState('');

  const results = useMemo(() => {
    const searchCriteria = { ...criteria, searchText };
    return searchWords(searchCriteria);
  }, [searchWords, criteria, searchText]);

  const suggestions = useMemo(() => {
    return searchText ? getSuggestions(searchText) : [];
  }, [getSuggestions, searchText]);

  return {
    criteria,
    setCriteria,
    searchText,
    setSearchText,
    results,
    suggestions,
    hasResults: results.length > 0,
    resultCount: results.length,
  };
};

/**
 * 词库统计Hook
 */
export const useWordLibraryStats = () => {
  const { getStatistics, getCategoryCoverage } = useWordLibrary();

  const stats = useMemo(() => getStatistics(), [getStatistics]);
  const coverage = useMemo(() => getCategoryCoverage(), [getCategoryCoverage]);

  return {
    stats,
    coverage,
    isEmpty: stats.totalWords === 0,
    hasEmptyCategories: stats.emptyCategories.length > 0,
  };
};

/**
 * 词语验证Hook
 */
export const useWordValidation = () => {
  const { validateWord } = useWordLibrary();
  const [validationResult, setValidationResult] = useState<{
    isValid: boolean;
    errors: string[];
  } | null>(null);

  const validate = useCallback((text: string, excludeWordId?: WordId) => {
    const result = validateWord(text, excludeWordId);
    setValidationResult(result);
    return result;
  }, [validateWord]);

  const clearValidation = useCallback(() => {
    setValidationResult(null);
  }, []);

  return {
    validate,
    clearValidation,
    validationResult,
    isValid: validationResult?.isValid ?? null,
    errors: validationResult?.errors ?? [],
  };
};

export default useWordLibrary;
