/**
 * 词库业务服务
 * 🎯 核心价值：提供词库相关的业务逻辑和工具函数
 * 📦 功能范围：词语验证、分类管理、搜索过滤、批量操作
 * 🔄 架构设计：基于函数式编程的服务层，支持组合和扩展
 */

import type { BasicColorType } from '../matrix/MatrixTypes';
import type {
  BatchOperationResult,
  ColorLevel,
  CreateWordParams,
  FilterCriteria,
  WordCategory,
  WordEntry,
  WordId,
  WordLibraryData
} from './WordLibraryTypes';
import {
  createDefaultWordValidation,
  validateChineseText
} from './WordLibraryTypes';

// ===== 词语验证服务 =====

export class WordValidationService {
  /**
   * 验证词语文本
   */
  static validateText(text: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!text || text.trim().length === 0) {
      errors.push('词语不能为空');
    }

    if (text.length > 10) {
      errors.push('词语长度不能超过10个字符');
    }

    if (text.length < 1) {
      errors.push('词语长度不能少于1个字符');
    }

    if (!validateChineseText(text)) {
      errors.push('词语必须包含有效的中文字符');
    }

    // 检查是否包含特殊字符
    if (/[^\u4e00-\u9fff]/.test(text)) {
      errors.push('词语只能包含中文字符');
    }

    // 检查是否包含生僻字或无效字符
    if (this.containsRareCharacters(text)) {
      errors.push('词语包含生僻字符，建议使用常用汉字');
    }

    // 检查词语结构合理性
    if (!this.isValidWordStructure(text)) {
      errors.push('词语结构不合理');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 检查是否包含生僻字符
   */
  static containsRareCharacters(text: string): boolean {
    // 常用汉字范围：4E00-9FFF，但排除一些生僻字区域
    const rareCharPattern = /[\u3400-\u4DBF\uF900-\uFAFF]/;
    return rareCharPattern.test(text);
  }

  /**
   * 验证词语结构合理性
   */
  static isValidWordStructure(text: string): boolean {
    // 检查是否有重复字符（超过一半）
    const charCount = new Map<string, number>();
    for (const char of text) {
      charCount.set(char, (charCount.get(char) || 0) + 1);
    }

    const maxCount = Math.max(...charCount.values());
    if (maxCount > Math.ceil(text.length / 2)) {
      return false; // 重复字符过多
    }

    return true;
  }

  /**
   * 验证词语是否重复
   */
  static checkDuplicate(
    text: string,
    wordLibrary: WordLibraryData,
    excludeWordId?: WordId
  ): boolean {
    for (const colorCategories of Object.values(wordLibrary.categories)) {
      for (const levelWords of Object.values(colorCategories)) {
        const duplicate = levelWords.find(
          word => word.text === text && word.id !== excludeWordId
        );
        if (duplicate) {
          return true;
        }
      }
    }
    return false;
  }

  /**
   * 完整验证词语
   */
  static validateWord(
    text: string,
    wordLibrary: WordLibraryData,
    excludeWordId?: WordId
  ): { isValid: boolean; errors: string[] } {
    const textValidation = this.validateText(text);

    if (!textValidation.isValid) {
      return textValidation;
    }

    const isDuplicate = this.checkDuplicate(text, wordLibrary, excludeWordId);
    if (isDuplicate) {
      return {
        isValid: false,
        errors: ['词语已存在'],
      };
    }

    return { isValid: true, errors: [] };
  }
}

// ===== 词库搜索和过滤服务 =====

export class WordLibrarySearchService {
  /**
   * 根据条件搜索词语
   */
  static searchWords(
    wordLibrary: WordLibraryData,
    criteria: FilterCriteria
  ): WordEntry[] {
    let results: WordEntry[] = [];

    // 如果指定了颜色和等级，只搜索特定分类
    if (criteria.color && criteria.level) {
      results = [...wordLibrary.categories[criteria.color][criteria.level]];
    } else if (criteria.color) {
      // 只指定颜色，搜索该颜色的所有等级
      Object.values(wordLibrary.categories[criteria.color]).forEach(levelWords => {
        results.push(...levelWords);
      });
    } else if (criteria.level) {
      // 只指定等级，搜索所有颜色的该等级
      Object.values(wordLibrary.categories).forEach(colorCategories => {
        results.push(...colorCategories[criteria.level!]);
      });
    } else {
      // 搜索所有词语
      Object.values(wordLibrary.categories).forEach(colorCategories => {
        Object.values(colorCategories).forEach(levelWords => {
          results.push(...levelWords);
        });
      });
    }

    // 文本搜索过滤
    if (criteria.searchText) {
      const searchText = criteria.searchText.toLowerCase();
      results = results.filter(word =>
        word.text.toLowerCase().includes(searchText)
      );
    }

    return results;
  }

  /**
   * 获取词语建议（基于输入的部分文本）
   */
  static getSuggestions(
    wordLibrary: WordLibraryData,
    partialText: string,
    maxResults: number = 10
  ): WordEntry[] {
    if (!partialText) return [];

    const results: WordEntry[] = [];

    Object.values(wordLibrary.categories).forEach(colorCategories => {
      Object.values(colorCategories).forEach(levelWords => {
        levelWords.forEach(word => {
          if (word.text.startsWith(partialText) && results.length < maxResults) {
            results.push(word);
          }
        });
      });
    });

    return results.sort((a, b) => a.text.localeCompare(b.text));
  }

  /**
   * 按使用频率排序词语
   */
  static sortByUsage(words: WordEntry[]): WordEntry[] {
    return [...words].sort((a, b) => {
      // 按使用次数降序排列
      if (b.metadata.usageCount !== a.metadata.usageCount) {
        return b.metadata.usageCount - a.metadata.usageCount;
      }
      // 使用次数相同时按最后使用时间排序
      const aLastUsed = a.metadata.lastUsed?.getTime() || 0;
      const bLastUsed = b.metadata.lastUsed?.getTime() || 0;
      return bLastUsed - aLastUsed;
    });
  }

  /**
   * 按创建时间排序词语
   */
  static sortByCreationTime(words: WordEntry[], ascending: boolean = false): WordEntry[] {
    return [...words].sort((a, b) => {
      const aTime = a.metadata.createdAt.getTime();
      const bTime = b.metadata.createdAt.getTime();
      return ascending ? aTime - bTime : bTime - aTime;
    });
  }
}

// ===== 词库统计服务 =====

export class WordLibraryStatsService {
  /**
   * 获取词库统计信息
   */
  static getStatistics(wordLibrary: WordLibraryData) {
    const stats = {
      totalWords: 0,
      wordsByColor: {} as Record<BasicColorType, number>,
      wordsByLevel: {} as Record<ColorLevel, number>,
      averageWordsPerCategory: 0,
      mostUsedWords: [] as WordEntry[],
      recentlyAddedWords: [] as WordEntry[],
      emptyCategories: [] as Array<{ color: BasicColorType; level: ColorLevel }>,
    };

    // 初始化计数器
    const colors: BasicColorType[] = ['red', 'cyan', 'yellow', 'purple', 'orange', 'green', 'blue', 'pink', 'black'];
    const levels: ColorLevel[] = [1, 2, 3, 4];

    colors.forEach(color => stats.wordsByColor[color] = 0);
    levels.forEach(level => stats.wordsByLevel[level] = 0);

    // 收集所有词语
    const allWords: WordEntry[] = [];

    colors.forEach(color => {
      levels.forEach(level => {
        const words = wordLibrary.categories[color][level];
        const wordCount = words.length;

        stats.totalWords += wordCount;
        stats.wordsByColor[color] += wordCount;
        stats.wordsByLevel[level] += wordCount;

        if (wordCount === 0) {
          stats.emptyCategories.push({ color, level });
        }

        allWords.push(...words);
      });
    });

    // 计算平均值
    const totalCategories = colors.length * levels.length;
    stats.averageWordsPerCategory = stats.totalWords / totalCategories;

    // 最常用的词语（前10个）
    stats.mostUsedWords = WordLibrarySearchService.sortByUsage(allWords).slice(0, 10);

    // 最近添加的词语（前10个）
    stats.recentlyAddedWords = WordLibrarySearchService.sortByCreationTime(allWords).slice(0, 10);

    return stats;
  }

  /**
   * 获取分类覆盖率
   */
  static getCategoryCoverage(wordLibrary: WordLibraryData): number {
    const colors: BasicColorType[] = ['red', 'cyan', 'yellow', 'purple', 'orange', 'green', 'blue', 'pink', 'black'];
    const levels: ColorLevel[] = [1, 2, 3, 4];

    let filledCategories = 0;
    const totalCategories = colors.length * levels.length;

    colors.forEach(color => {
      levels.forEach(level => {
        if (wordLibrary.categories[color][level].length > 0) {
          filledCategories++;
        }
      });
    });

    return (filledCategories / totalCategories) * 100;
  }
}

// ===== 批量操作服务 =====

export class WordLibraryBatchService {
  /**
   * 批量添加词语
   */
  static async batchAddWords(
    words: Array<{ text: string; category: WordCategory }>,
    wordLibrary: WordLibraryData,
    addWordFn: (word: CreateWordParams) => Promise<WordId>
  ): Promise<BatchOperationResult> {
    const result: BatchOperationResult = {
      success: true,
      processed: 0,
      errors: [],
    };

    for (const wordData of words) {
      try {
        // 验证词语
        const validation = WordValidationService.validateWord(
          wordData.text,
          wordLibrary
        );

        if (!validation.isValid) {
          result.errors.push(`词语"${wordData.text}": ${validation.errors.join(', ')}`);
          continue;
        }

        // 添加词语
        const createParams: CreateWordParams = {
          text: wordData.text,
          category: wordData.category,
          validation: createDefaultWordValidation(wordData.text),
        };
        await addWordFn(createParams);
        result.processed++;
      } catch (error) {
        result.errors.push(`词语"${wordData.text}": ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }

    result.success = result.errors.length === 0;
    return result;
  }

  /**
   * 批量删除词语
   */
  static async batchRemoveWords(
    wordIds: WordId[],
    removeWordFn: (wordId: WordId) => Promise<void>
  ): Promise<BatchOperationResult> {
    const result: BatchOperationResult = {
      success: true,
      processed: 0,
      errors: [],
    };

    for (const wordId of wordIds) {
      try {
        await removeWordFn(wordId);
        result.processed++;
      } catch (error) {
        result.errors.push(`词语ID"${wordId}": ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }

    result.success = result.errors.length === 0;
    return result;
  }

  /**
   * 导入词语数据
   */
  static parseImportData(csvData: string): Array<{ text: string; category: WordCategory; validation: { isValid: boolean } }> {
    const lines = csvData.split('\n').filter(line => line.trim());
    const words: Array<{ text: string; category: WordCategory; validation: { isValid: boolean } }> = [];

    // 跳过标题行
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      const [text, color, level] = line.split(',').map(s => s.trim());

      if (text && color && level) {
        words.push({
          text,
          category: {
            color: color as BasicColorType,
            level: parseInt(level) as ColorLevel,
          },
          validation: { isValid: true },
        });
      }
    }

    return words;
  }

  /**
   * 导出词语数据为CSV
   */
  static exportToCsv(wordLibrary: WordLibraryData): string {
    const lines = ['词语,颜色,等级,创建时间,使用次数'];

    Object.entries(wordLibrary.categories).forEach(([color, levels]) => {
      Object.entries(levels).forEach(([level, words]) => {
        words.forEach(word => {
          lines.push([
            word.text,
            color,
            level,
            word.metadata.createdAt.toISOString(),
            word.metadata.usageCount.toString(),
          ].join(','));
        });
      });
    });

    return lines.join('\n');
  }
}

// ===== 导出所有服务 =====

export const WordLibraryServices = {
  validation: WordValidationService,
  search: WordLibrarySearchService,
  stats: WordLibraryStatsService,
  batch: WordLibraryBatchService,
};

export default WordLibraryServices;
