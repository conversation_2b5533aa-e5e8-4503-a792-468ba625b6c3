/**
 * 版本管理服务
 * 🎯 核心价值：提供完整的词库版本管理功能，支持快照、比较、恢复
 * 📦 功能范围：版本创建、保存、加载、比较、导出、导入
 * 🔄 架构设计：基于快照的版本系统，支持增量比较和数据完整性验证
 */

import type {
  CellWordBinding,
  CellWordBindings,
  WordLibraryData,
  WordVersion
} from './WordLibraryTypes';
import { parseCellKey } from './WordLibraryTypes';

// ===== 版本比较结果 =====

/** 版本差异类型 */
export type VersionDiffType = 'added' | 'removed' | 'modified' | 'unchanged';

/** 单个绑定差异 */
export interface BindingDiff {
  cellKey: string;
  type: VersionDiffType;
  before?: CellWordBinding;
  after?: CellWordBinding;
  coordinate: { x: number; y: number };
}

/** 版本比较结果 */
export interface VersionComparison {
  /** 源版本ID */
  fromVersionId: string;
  /** 目标版本ID */
  toVersionId: string;
  /** 比较时间 */
  comparedAt: Date;
  /** 绑定差异列表 */
  bindingDiffs: BindingDiff[];
  /** 统计信息 */
  statistics: {
    totalChanges: number;
    addedBindings: number;
    removedBindings: number;
    modifiedBindings: number;
    unchangedBindings: number;
  };
  /** 影响的区域 */
  affectedRegions: Array<{
    region: string;
    changeCount: number;
    changeTypes: VersionDiffType[];
  }>;
}

/** 版本快照数据 */
export interface VersionSnapshot {
  /** 版本信息 */
  version: WordVersion;
  /** 词库数据 */
  wordLibrary: WordLibraryData;
  /** 绑定关系 */
  bindings: CellWordBinding[];
  /** 快照元数据 */
  metadata: {
    createdBy: string;
    createdAt: Date;
    size: number;
    checksum: string;
  };
}

// ===== 版本管理配置 =====

export interface VersionManagementConfig {
  /** 最大版本数量 */
  maxVersions: number;
  /** 是否启用自动保存 */
  enableAutoSave: boolean;
  /** 自动保存间隔（毫秒） */
  autoSaveInterval: number;
  /** 是否启用压缩 */
  enableCompression: boolean;
  /** 版本命名模式 */
  namingPattern: 'timestamp' | 'sequential' | 'custom';
}

const DEFAULT_CONFIG: VersionManagementConfig = {
  maxVersions: 50,
  enableAutoSave: false,
  autoSaveInterval: 5 * 60 * 1000, // 5分钟
  enableCompression: true,
  namingPattern: 'timestamp',
};

// ===== 版本管理服务 =====

export class VersionManagementService {
  private config: VersionManagementConfig;
  private autoSaveTimer: NodeJS.Timeout | null = null;

  constructor(config: Partial<VersionManagementConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * 创建版本快照
   */
  public createVersionSnapshot(
    name: string,
    description: string,
    wordLibrary: WordLibraryData,
    cellWordBindings: CellWordBindings,
    createdBy = 'user'
  ): VersionSnapshot {
    const versionId = this.generateVersionId();
    const bindings = Array.from(cellWordBindings.values());
    const checksum = this.calculateChecksum(wordLibrary, bindings);

    const version: WordVersion = {
      id: versionId,
      name,
      description,
      createdAt: new Date(),
      bindings,
      metadata: {
        wordCount: this.countWords(wordLibrary),
        totalBindings: bindings.length,
        coverageRate: bindings.length / (33 * 33),
        duplicateCount: 0, // 需要从重复检测服务获取
      },
      checksum,
    };

    const snapshot: VersionSnapshot = {
      version,
      wordLibrary: this.cloneWordLibrary(wordLibrary),
      bindings: bindings.map(binding => ({ ...binding })),
      metadata: {
        createdBy,
        createdAt: new Date(),
        size: this.calculateSnapshotSize(wordLibrary, bindings),
        checksum,
      },
    };

    return snapshot;
  }

  /**
   * 保存版本到存储
   */
  public async saveVersion(
    snapshot: VersionSnapshot,
    storage: 'localStorage' | 'indexedDB' | 'server' = 'localStorage'
  ): Promise<void> {
    try {
      const serializedData = this.serializeSnapshot(snapshot);

      switch (storage) {
        case 'localStorage':
          await this.saveToLocalStorage(snapshot.version.id, serializedData);
          break;
        case 'indexedDB':
          await this.saveToIndexedDB(snapshot.version.id, serializedData);
          break;
        case 'server':
          await this.saveToServer(snapshot.version.id, serializedData);
          break;
      }
    } catch (error) {
      throw new Error(`保存版本失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 从存储加载版本
   */
  public async loadVersion(
    versionId: string,
    storage: 'localStorage' | 'indexedDB' | 'server' = 'localStorage'
  ): Promise<VersionSnapshot> {
    try {
      let serializedData: string;

      switch (storage) {
        case 'localStorage':
          serializedData = await this.loadFromLocalStorage(versionId);
          break;
        case 'indexedDB':
          serializedData = await this.loadFromIndexedDB(versionId);
          break;
        case 'server':
          serializedData = await this.loadFromServer(versionId);
          break;
        default:
          throw new Error('不支持的存储类型');
      }

      const snapshot = this.deserializeSnapshot(serializedData);

      // 验证数据完整性
      if (!this.validateSnapshot(snapshot)) {
        throw new Error('版本数据损坏');
      }

      return snapshot;
    } catch (error) {
      throw new Error(`加载版本失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 比较两个版本
   */
  public compareVersions(
    fromSnapshot: VersionSnapshot,
    toSnapshot: VersionSnapshot
  ): VersionComparison {
    const fromBindings = new Map<string, CellWordBinding>();
    const toBindings = new Map<string, CellWordBinding>();

    // 构建绑定映射
    fromSnapshot.bindings.forEach(binding => {
      fromBindings.set(binding.cellKey, binding);
    });

    toSnapshot.bindings.forEach(binding => {
      toBindings.set(binding.cellKey, binding);
    });

    const bindingDiffs: BindingDiff[] = [];
    const allCellKeys = new Set([...fromBindings.keys(), ...toBindings.keys()]);

    // 比较每个单元格的绑定
    allCellKeys.forEach(cellKey => {
      const fromBinding = fromBindings.get(cellKey);
      const toBinding = toBindings.get(cellKey);
      const coordinate = parseCellKey(cellKey);

      if (!fromBinding && toBinding) {
        // 新增绑定
        bindingDiffs.push({
          cellKey,
          type: 'added',
          after: toBinding,
          coordinate,
        });
      } else if (fromBinding && !toBinding) {
        // 删除绑定
        bindingDiffs.push({
          cellKey,
          type: 'removed',
          before: fromBinding,
          coordinate,
        });
      } else if (fromBinding && toBinding) {
        if (this.areBindingsEqual(fromBinding, toBinding)) {
          // 未变化
          bindingDiffs.push({
            cellKey,
            type: 'unchanged',
            before: fromBinding,
            after: toBinding,
            coordinate,
          });
        } else {
          // 修改绑定
          bindingDiffs.push({
            cellKey,
            type: 'modified',
            before: fromBinding,
            after: toBinding,
            coordinate,
          });
        }
      }
    });

    // 计算统计信息
    const statistics = {
      totalChanges: bindingDiffs.filter(diff => diff.type !== 'unchanged').length,
      addedBindings: bindingDiffs.filter(diff => diff.type === 'added').length,
      removedBindings: bindingDiffs.filter(diff => diff.type === 'removed').length,
      modifiedBindings: bindingDiffs.filter(diff => diff.type === 'modified').length,
      unchangedBindings: bindingDiffs.filter(diff => diff.type === 'unchanged').length,
    };

    // 分析影响区域
    const affectedRegions = this.analyzeAffectedRegions(bindingDiffs);

    return {
      fromVersionId: fromSnapshot.version.id,
      toVersionId: toSnapshot.version.id,
      comparedAt: new Date(),
      bindingDiffs,
      statistics,
      affectedRegions,
    };
  }

  /**
   * 导出版本为JSON
   */
  public exportVersionToJSON(snapshot: VersionSnapshot): string {
    return JSON.stringify(snapshot, null, 2);
  }

  /**
   * 从JSON导入版本
   */
  public importVersionFromJSON(jsonData: string): VersionSnapshot {
    try {
      const snapshot = JSON.parse(jsonData) as VersionSnapshot;

      if (!this.validateSnapshot(snapshot)) {
        throw new Error('导入的版本数据格式不正确');
      }

      return snapshot;
    } catch (error) {
      throw new Error(`导入版本失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 启用自动保存
   */
  public enableAutoSave(
    saveCallback: () => Promise<void>
  ): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
    }

    if (this.config.enableAutoSave) {
      this.autoSaveTimer = setInterval(async () => {
        try {
          await saveCallback();
        } catch (error) {
          console.error('自动保存失败:', error);
        }
      }, this.config.autoSaveInterval);
    }
  }

  /**
   * 禁用自动保存
   */
  public disableAutoSave(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
      this.autoSaveTimer = null;
    }
  }

  // ===== 私有方法 =====

  private generateVersionId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 9);

    switch (this.config.namingPattern) {
      case 'timestamp':
        return `v_${timestamp}`;
      case 'sequential':
        return `v_${timestamp}_${random}`;
      case 'custom':
      default:
        return `version_${timestamp}_${random}`;
    }
  }

  private calculateChecksum(wordLibrary: WordLibraryData, bindings: CellWordBinding[]): string {
    const data = JSON.stringify({ wordLibrary, bindings });
    // 简单的校验和计算（实际应用中应使用更强的哈希算法）
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString(16);
  }

  private countWords(wordLibrary: WordLibraryData): number {
    let count = 0;
    Object.values(wordLibrary.categories).forEach(colorCategories => {
      Object.values(colorCategories).forEach(levelWords => {
        count += levelWords.length;
      });
    });
    return count;
  }

  private getCategoryStats(wordLibrary: WordLibraryData): Record<string, number> {
    const stats: Record<string, number> = {};
    Object.entries(wordLibrary.categories).forEach(([color, colorCategories]) => {
      let colorCount = 0;
      Object.values(colorCategories).forEach(levelWords => {
        colorCount += levelWords.length;
      });
      stats[color] = colorCount;
    });
    return stats;
  }

  private cloneWordLibrary(wordLibrary: WordLibraryData): WordLibraryData {
    return JSON.parse(JSON.stringify(wordLibrary));
  }

  private calculateSnapshotSize(wordLibrary: WordLibraryData, bindings: CellWordBinding[]): number {
    const data = JSON.stringify({ wordLibrary, bindings });
    return new Blob([data]).size;
  }

  private serializeSnapshot(snapshot: VersionSnapshot): string {
    if (this.config.enableCompression) {
      // 这里可以添加压缩逻辑
      return JSON.stringify(snapshot);
    }
    return JSON.stringify(snapshot);
  }

  private deserializeSnapshot(data: string): VersionSnapshot {
    return JSON.parse(data) as VersionSnapshot;
  }

  private validateSnapshot(snapshot: VersionSnapshot): boolean {
    try {
      // 验证基本结构
      if (!snapshot.version || !snapshot.wordLibrary || !snapshot.bindings || !snapshot.metadata) {
        return false;
      }

      // 验证版本信息
      if (!snapshot.version.id || !snapshot.version.name || !snapshot.version.createdAt) {
        return false;
      }

      // 验证校验和
      const calculatedChecksum = this.calculateChecksum(snapshot.wordLibrary, snapshot.bindings);
      if (calculatedChecksum !== snapshot.version.checksum) {
        return false;
      }

      return true;
    } catch {
      return false;
    }
  }

  private areBindingsEqual(binding1: CellWordBinding, binding2: CellWordBinding): boolean {
    return binding1.cellKey === binding2.cellKey &&
      binding1.wordId === binding2.wordId &&
      binding1.isTemporary === binding2.isTemporary;
  }

  private analyzeAffectedRegions(bindingDiffs: BindingDiff[]): Array<{
    region: string;
    changeCount: number;
    changeTypes: VersionDiffType[];
  }> {
    const regionSize = 11; // 11x11 区域
    const regions = new Map<string, { count: number; types: Set<VersionDiffType> }>();

    bindingDiffs.forEach(diff => {
      if (diff.type === 'unchanged') return;

      const regionX = Math.floor(diff.coordinate.x / regionSize);
      const regionY = Math.floor(diff.coordinate.y / regionSize);
      const regionKey = `${regionX},${regionY}`;

      const region = regions.get(regionKey) || { count: 0, types: new Set() };
      region.count++;
      region.types.add(diff.type);
      regions.set(regionKey, region);
    });

    return Array.from(regions.entries())
      .map(([region, data]) => ({
        region,
        changeCount: data.count,
        changeTypes: Array.from(data.types),
      }))
      .sort((a, b) => b.changeCount - a.changeCount);
  }

  // ===== 存储方法 =====

  private async saveToLocalStorage(versionId: string, data: string): Promise<void> {
    try {
      localStorage.setItem(`word_version_${versionId}`, data);
    } catch (error) {
      throw new Error('LocalStorage保存失败');
    }
  }

  private async loadFromLocalStorage(versionId: string): Promise<string> {
    const data = localStorage.getItem(`word_version_${versionId}`);
    if (!data) {
      throw new Error('版本不存在');
    }
    return data;
  }

  private async saveToIndexedDB(versionId: string, data: string): Promise<void> {
    // IndexedDB实现
    throw new Error('IndexedDB存储暂未实现');
  }

  private async loadFromIndexedDB(versionId: string): Promise<string> {
    // IndexedDB实现
    throw new Error('IndexedDB存储暂未实现');
  }

  private async saveToServer(versionId: string, data: string): Promise<void> {
    // 服务器存储实现
    throw new Error('服务器存储暂未实现');
  }

  private async loadFromServer(versionId: string): Promise<string> {
    // 服务器存储实现
    throw new Error('服务器存储暂未实现');
  }
}
