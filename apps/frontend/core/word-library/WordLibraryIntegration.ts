/**
 * 词库系统与矩阵系统集成服务
 * 🎯 核心价值：连接词库状态管理与矩阵状态管理，确保数据同步和一致性
 * 📦 功能范围：状态同步、事件处理、数据转换、交互协调
 * 🔄 架构设计：基于观察者模式的双向数据绑定，支持实时同步
 */

import { useMatrixStore } from '../matrix/MatrixStore';
import type { BasicColorType, CellData } from '../matrix/MatrixTypes';
import { useWordLibraryStore } from './WordLibraryStore';
import type {
  CellWordBinding,
  ColorLevel,
  WordEntry,
  WordId
} from './WordLibraryTypes';
import { generateCellKey, parseCellKey } from './WordLibraryTypes';

// ===== 集成服务类型 =====

interface WordLibraryIntegrationConfig {
  autoSyncEnabled: boolean;
  duplicateHighlightEnabled: boolean;
  previewEnabled: boolean;
  syncInterval: number;
}

interface CellWordInfo {
  cellData: CellData;
  wordEntry?: WordEntry;
  binding?: CellWordBinding;
  isDuplicate: boolean;
}

// ===== 集成服务实现 =====

export class WordLibraryIntegrationService {
  private matrixStore: any;
  private wordLibraryStore: any;
  private config: WordLibraryIntegrationConfig;
  private syncTimer: NodeJS.Timeout | null = null;

  constructor(
    matrixStore: any,
    wordLibraryStore: any,
    config: Partial<WordLibraryIntegrationConfig> = {}
  ) {
    this.matrixStore = matrixStore;
    this.wordLibraryStore = wordLibraryStore;
    this.config = {
      autoSyncEnabled: true,
      duplicateHighlightEnabled: true,
      previewEnabled: true,
      syncInterval: 1000,
      ...config,
    };

    this.initialize();
  }

  // ===== 初始化和清理 =====

  private initialize(): void {
    if (this.config.autoSyncEnabled) {
      this.startAutoSync();
    }

    // 初始同步
    this.syncWordLibraryToMatrix();
  }

  public destroy(): void {
    this.stopAutoSync();
  }

  private startAutoSync(): void {
    if (this.syncTimer) return;

    this.syncTimer = setInterval(() => {
      this.syncWordLibraryToMatrix();
    }, this.config.syncInterval);
  }

  private stopAutoSync(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = null;
    }
  }

  // ===== 核心同步方法 =====

  /**
   * 将词库绑定同步到矩阵单元格
   */
  public syncWordLibraryToMatrix(): void {
    const state = this.wordLibraryStore.getState();
    const bindings = state.cellWordBindings;
    const duplicateWords = state.duplicateDetection.duplicateWords;
    const wordLibrary = state.wordLibrary;

    // 批量更新单元格
    const updates: Array<{ x: number; y: number; data: Partial<CellData> }> = [];

    // 清除所有单元格的词语信息
    for (let x = 0; x < 33; x++) {
      for (let y = 0; y < 33; y++) {
        updates.push({
          x, y,
          data: {
            word: undefined,
            wordId: undefined,
            isDuplicate: false,
            isWordPreview: false,
          }
        });
      }
    }

    // 应用词语绑定
    bindings.forEach((binding: any, cellKey: any) => {
      const { x, y } = parseCellKey(cellKey);
      const word = this.findWordById(wordLibrary, binding.wordId);

      if (word) {
        const isDuplicate = duplicateWords.has(word.text);

        updates.push({
          x, y,
          data: {
            word: word.text,
            wordId: word.id,
            isDuplicate,
            isWordPreview: binding.isTemporary || false,
          }
        });
      }
    });

    // 批量更新矩阵
    this.matrixStore.getState().updateCells(updates);
  }

  /**
   * 根据单元格信息获取可用词语
   */
  public getAvailableWordsForCell(x: number, y: number): WordEntry[] {
    const cellData = this.matrixStore.getState().data.cells.get(generateCellKey(x, y));
    if (!cellData || !cellData.color || !cellData.level) {
      return [];
    }

    return this.wordLibraryStore.getState().getWordsByCategory(
      cellData.color as BasicColorType,
      cellData.level as ColorLevel
    );
  }

  /**
   * 激活单元格的词语选择模式
   */
  public activateWordSelectionForCell(x: number, y: number): void {
    const availableWords = this.getAvailableWordsForCell(x, y);

    // 更新词库导航状态
    this.wordLibraryStore.setState((state: any) => ({
      ...state,
      navigationState: {
        ...state.navigationState,
        isActive: true,
        targetCell: { x, y },
        availableWords,
        selectedIndex: 0,
        previewMode: false,
      }
    }));
  }

  /**
   * 处理单元格双击事件
   */
  public handleCellDoubleClick(x: number, y: number): void {
    this.activateWordSelectionForCell(x, y);
  }

  /**
   * 处理键盘导航
   */
  public handleKeyboardNavigation(key: string): boolean {
    const navigationState = this.wordLibraryStore.getState().navigationState;

    if (!navigationState.isActive) {
      return false;
    }

    switch (key) {
      case 'ArrowLeft':
        this.wordLibraryStore.getState().navigateWords('left');
        return true;

      case 'ArrowRight':
        this.wordLibraryStore.getState().navigateWords('right');
        return true;

      case 'ArrowUp':
        if (this.config.previewEnabled) {
          this.previewCurrentSelection();
        }
        return true;

      case 'ArrowDown':
        if (this.config.previewEnabled) {
          this.wordLibraryStore.getState().clearPreview();
        }
        return true;

      case 'Enter':
        this.wordLibraryStore.getState().confirmSelection();
        return true;

      case 'Escape':
        this.wordLibraryStore.getState().cancelSelection();
        return true;

      default:
        return false;
    }
  }

  /**
   * 预览当前选中的词语
   */
  private previewCurrentSelection(): void {
    const navigationState = this.wordLibraryStore.getState().navigationState;

    if (!navigationState.isActive || !navigationState.targetCell) {
      return;
    }

    const selectedWord = navigationState.availableWords[navigationState.selectedIndex];
    if (selectedWord) {
      this.wordLibraryStore.getState().previewWordInCell(
        navigationState.targetCell.x,
        navigationState.targetCell.y,
        selectedWord.id
      );
    }
  }

  // ===== 数据查询方法 =====

  /**
   * 获取单元格的完整词语信息
   */
  public getCellWordInfo(x: number, y: number): CellWordInfo {
    const cellKey = generateCellKey(x, y);
    const cellData = this.matrixStore.getState().data.cells.get(cellKey);
    const binding = this.wordLibraryStore.getState().cellWordBindings.get(cellKey);

    let wordEntry: WordEntry | undefined;
    let isDuplicate = false;

    if (binding) {
      const wordLibrary = this.wordLibraryStore.getState().wordLibrary;
      wordEntry = this.findWordById(wordLibrary, binding.wordId);

      if (wordEntry) {
        const duplicateWords = this.wordLibraryStore.getState().duplicateDetection.duplicateWords;
        isDuplicate = duplicateWords.has(wordEntry.text);
      }
    }

    return {
      cellData: cellData || { x, y, isActive: true, isSelected: false, isHovered: false },
      wordEntry,
      binding,
      isDuplicate,
    };
  }

  /**
   * 检查单元格是否可以绑定词语
   */
  public canBindWordToCell(x: number, y: number): boolean {
    const cellData = this.matrixStore.getState().data.cells.get(generateCellKey(x, y));
    return !!(cellData && cellData.color && cellData.level);
  }

  /**
   * 获取所有重复词语的位置
   */
  public getDuplicateWordPositions(): Map<string, Array<{ x: number; y: number }>> {
    const duplicateDetection = this.wordLibraryStore.getState().duplicateDetection;
    const positions = new Map<string, Array<{ x: number; y: number }>>();

    duplicateDetection.duplicateGroups.forEach((group: any, wordText: any) => {
      positions.set(wordText, group.positions);
    });

    return positions;
  }

  // ===== 工具方法 =====

  private findWordById(wordLibrary: any, wordId: WordId): WordEntry | undefined {
    for (const colorCategories of Object.values(wordLibrary.categories)) {
      for (const levelWords of Object.values(colorCategories as any)) {
        const word = (levelWords as WordEntry[]).find(w => w.id === wordId);
        if (word) return word;
      }
    }
    return undefined;
  }

  // ===== 配置管理 =====

  public updateConfig(newConfig: Partial<WordLibraryIntegrationConfig>): void {
    this.config = { ...this.config, ...newConfig };

    if (newConfig.autoSyncEnabled !== undefined) {
      if (newConfig.autoSyncEnabled) {
        this.startAutoSync();
      } else {
        this.stopAutoSync();
      }
    }
  }

  public getConfig(): WordLibraryIntegrationConfig {
    return { ...this.config };
  }

  // ===== 事件处理 =====

  /**
   * 处理词库变更事件
   */
  public onWordLibraryChange(): void {
    if (this.config.autoSyncEnabled) {
      this.syncWordLibraryToMatrix();
    }
  }

  /**
   * 处理重复检测完成事件
   */
  public onDuplicateDetectionComplete(): void {
    if (this.config.duplicateHighlightEnabled) {
      this.syncWordLibraryToMatrix();
    }
  }
}

// ===== 集成钩子 =====

/**
 * 使用词库集成服务的React钩子
 */
export const useWordLibraryIntegration = (
  config?: Partial<WordLibraryIntegrationConfig>
) => {
  const matrixStore = useMatrixStore();
  const wordLibraryStore = useWordLibraryStore();

  // 创建集成服务实例（在实际使用中应该使用useMemo或useRef）
  const integrationService = new WordLibraryIntegrationService(
    matrixStore,
    wordLibraryStore,
    config
  );

  return {
    service: integrationService,
    syncWordLibraryToMatrix: () => integrationService.syncWordLibraryToMatrix(),
    getAvailableWordsForCell: (x: number, y: number) =>
      integrationService.getAvailableWordsForCell(x, y),
    activateWordSelectionForCell: (x: number, y: number) =>
      integrationService.activateWordSelectionForCell(x, y),
    handleCellDoubleClick: (x: number, y: number) =>
      integrationService.handleCellDoubleClick(x, y),
    handleKeyboardNavigation: (key: string) =>
      integrationService.handleKeyboardNavigation(key),
    getCellWordInfo: (x: number, y: number) =>
      integrationService.getCellWordInfo(x, y),
    canBindWordToCell: (x: number, y: number) =>
      integrationService.canBindWordToCell(x, y),
    getDuplicateWordPositions: () =>
      integrationService.getDuplicateWordPositions(),
  };
};

export default WordLibraryIntegrationService;
