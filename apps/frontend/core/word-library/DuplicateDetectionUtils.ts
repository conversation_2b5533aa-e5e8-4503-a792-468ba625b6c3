/**
 * 重复检测工具函数
 * 🎯 核心价值：提供便捷的重复检测工具和分析功能
 * 📦 功能范围：批量检测、性能分析、报告生成、数据导出
 * 🔄 架构设计：基于函数式编程的工具集，支持链式调用
 */

import type {
  CellWordBinding,
  CellWordBindings,
  DuplicateDetection,
  DuplicateGroup,
  DuplicateStatistics,
  WordEntry,
  WordLibraryData,
} from './WordLibraryTypes';
import { parseCellKey } from './WordLibraryTypes';

// ===== 分析工具 =====

/** 重复检测分析结果 */
export interface DuplicateAnalysisResult {
  /** 重复检测结果 */
  detection: DuplicateDetection;
  /** 性能指标 */
  performance: {
    detectionTime: number;
    processedBindings: number;
    cacheHitRate: number;
  };
  /** 详细分析 */
  analysis: {
    topDuplicates: Array<{ wordText: string; count: number; positions: Array<{ x: number; y: number }> }>;
    duplicatesByCategory: Map<string, number>;
    duplicatesByLevel: Map<number, number>;
    hotspots: Array<{ region: string; duplicateCount: number }>;
  };
}

/** 重复检测报告 */
export interface DuplicateReport {
  /** 报告生成时间 */
  generatedAt: Date;
  /** 总体统计 */
  summary: {
    totalWords: number;
    totalDuplicates: number;
    duplicateRate: number;
    maxUsageCount: number;
    averageUsageCount: number;
  };
  /** 详细数据 */
  details: {
    duplicateGroups: DuplicateGroup[];
    categoryBreakdown: Array<{ category: string; duplicates: number; percentage: number }>;
    levelBreakdown: Array<{ level: number; duplicates: number; percentage: number }>;
    positionHeatmap: Array<{ x: number; y: number; duplicateCount: number }>;
  };
  /** 建议 */
  recommendations: string[];
}

// ===== 工具函数 =====

/**
 * 批量重复检测分析
 */
export function analyzeDuplicates(
  cellWordBindings: CellWordBindings,
  wordLibrary: WordLibraryData,
  detection: DuplicateDetection
): DuplicateAnalysisResult {
  const startTime = performance.now();

  // 分析重复词语
  const topDuplicates = Array.from(detection.duplicateGroups.values())
    .sort((a, b) => b.usageCount - a.usageCount)
    .slice(0, 10)
    .map(group => ({
      wordText: group.wordText,
      count: group.usageCount,
      positions: group.positions,
    }));

  // 按分类统计重复
  const duplicatesByCategory = new Map<string, number>();
  const duplicatesByLevel = new Map<number, number>();

  detection.duplicateGroups.forEach(group => {
    group.wordIds.forEach(wordId => {
      const word = findWordById(wordLibrary, wordId);
      if (word) {
        const categoryKey = word.category.color;
        const level = word.category.level;

        duplicatesByCategory.set(categoryKey, (duplicatesByCategory.get(categoryKey) || 0) + 1);
        duplicatesByLevel.set(level, (duplicatesByLevel.get(level) || 0) + 1);
      }
    });
  });

  // 分析热点区域
  const hotspots = analyzeHotspots(detection.duplicateGroups);

  const endTime = performance.now();

  return {
    detection,
    performance: {
      detectionTime: endTime - startTime,
      processedBindings: cellWordBindings.size,
      cacheHitRate: 0, // 需要从服务获取
    },
    analysis: {
      topDuplicates,
      duplicatesByCategory,
      duplicatesByLevel,
      hotspots,
    },
  };
}

/**
 * 生成重复检测报告
 */
export function generateDuplicateReport(
  cellWordBindings: CellWordBindings,
  wordLibrary: WordLibraryData,
  detection: DuplicateDetection
): DuplicateReport {
  const totalWords = cellWordBindings.size;
  const totalDuplicates = detection.statistics.totalDuplicates;
  const duplicateRate = detection.statistics.duplicateRate;

  // 计算平均使用次数
  const usageCounts = Array.from(detection.duplicateGroups.values()).map(g => g.usageCount);
  const averageUsageCount = usageCounts.length > 0 
    ? usageCounts.reduce((sum, count) => sum + count, 0) / usageCounts.length 
    : 0;

  // 分类统计
  const categoryStats = new Map<string, number>();
  const levelStats = new Map<number, number>();

  detection.duplicateGroups.forEach(group => {
    group.wordIds.forEach(wordId => {
      const word = findWordById(wordLibrary, wordId);
      if (word) {
        const category = word.category.color;
        const level = word.category.level;

        categoryStats.set(category, (categoryStats.get(category) || 0) + 1);
        levelStats.set(level, (levelStats.get(level) || 0) + 1);
      }
    });
  });

  // 转换为百分比
  const categoryBreakdown = Array.from(categoryStats.entries()).map(([category, count]) => ({
    category,
    duplicates: count,
    percentage: totalDuplicates > 0 ? (count / totalDuplicates) * 100 : 0,
  }));

  const levelBreakdown = Array.from(levelStats.entries()).map(([level, count]) => ({
    level,
    duplicates: count,
    percentage: totalDuplicates > 0 ? (count / totalDuplicates) * 100 : 0,
  }));

  // 位置热力图
  const positionHeatmap = generatePositionHeatmap(detection.duplicateGroups);

  // 生成建议
  const recommendations = generateRecommendations(detection, categoryBreakdown, levelBreakdown);

  return {
    generatedAt: new Date(),
    summary: {
      totalWords,
      totalDuplicates,
      duplicateRate,
      maxUsageCount: detection.statistics.maxUsageCount,
      averageUsageCount,
    },
    details: {
      duplicateGroups: Array.from(detection.duplicateGroups.values()),
      categoryBreakdown,
      levelBreakdown,
      positionHeatmap,
    },
    recommendations,
  };
}

/**
 * 分析热点区域
 */
function analyzeHotspots(duplicateGroups: Map<string, DuplicateGroup>): Array<{ region: string; duplicateCount: number }> {
  const regionSize = 11; // 11x11 区域
  const regions = new Map<string, number>();

  duplicateGroups.forEach(group => {
    group.positions.forEach(pos => {
      const regionX = Math.floor(pos.x / regionSize);
      const regionY = Math.floor(pos.y / regionSize);
      const regionKey = `${regionX},${regionY}`;

      regions.set(regionKey, (regions.get(regionKey) || 0) + 1);
    });
  });

  return Array.from(regions.entries())
    .map(([region, count]) => ({ region, duplicateCount: count }))
    .sort((a, b) => b.duplicateCount - a.duplicateCount)
    .slice(0, 5);
}

/**
 * 生成位置热力图数据
 */
function generatePositionHeatmap(duplicateGroups: Map<string, DuplicateGroup>): Array<{ x: number; y: number; duplicateCount: number }> {
  const positionCounts = new Map<string, number>();

  duplicateGroups.forEach(group => {
    group.positions.forEach(pos => {
      const key = `${pos.x},${pos.y}`;
      positionCounts.set(key, (positionCounts.get(key) || 0) + 1);
    });
  });

  return Array.from(positionCounts.entries())
    .map(([key, count]) => {
      const [x, y] = key.split(',').map(Number);
      return { x, y, duplicateCount: count };
    })
    .sort((a, b) => b.duplicateCount - a.duplicateCount);
}

/**
 * 生成优化建议
 */
function generateRecommendations(
  detection: DuplicateDetection,
  categoryBreakdown: Array<{ category: string; duplicates: number; percentage: number }>,
  levelBreakdown: Array<{ level: number; duplicates: number; percentage: number }>
): string[] {
  const recommendations: string[] = [];

  // 基于重复率的建议
  if (detection.statistics.duplicateRate > 0.3) {
    recommendations.push('重复率较高（超过30%），建议检查词库设计和词语选择策略');
  }

  // 基于最大使用次数的建议
  if (detection.statistics.maxUsageCount > 5) {
    recommendations.push(`发现高频重复词语（最多使用${detection.statistics.maxUsageCount}次），建议考虑替换为同义词`);
  }

  // 基于分类分布的建议
  const topCategory = categoryBreakdown.reduce((max, current) => 
    current.duplicates > max.duplicates ? current : max, 
    { category: '', duplicates: 0, percentage: 0 }
  );

  if (topCategory.percentage > 50) {
    recommendations.push(`${topCategory.category}类别的重复词语过多（${topCategory.percentage.toFixed(1)}%），建议增加该类别的词语多样性`);
  }

  // 基于等级分布的建议
  const topLevel = levelBreakdown.reduce((max, current) => 
    current.duplicates > max.duplicates ? current : max,
    { level: 0, duplicates: 0, percentage: 0 }
  );

  if (topLevel.percentage > 40) {
    recommendations.push(`等级${topLevel.level}的重复词语较多（${topLevel.percentage.toFixed(1)}%），建议平衡各等级的词语分布`);
  }

  // 基于重复组数量的建议
  if (detection.duplicateGroups.size === 0) {
    recommendations.push('当前没有重复词语，词库设计良好');
  } else if (detection.duplicateGroups.size < 5) {
    recommendations.push('重复词语数量较少，整体词库质量良好');
  }

  return recommendations;
}

/**
 * 导出重复检测数据为CSV格式
 */
export function exportDuplicateDataToCSV(detection: DuplicateDetection): string {
  const headers = ['词语文本', '使用次数', '位置列表', '词语ID列表'];
  const rows = [headers.join(',')];

  detection.duplicateGroups.forEach(group => {
    const positions = group.positions.map(pos => `(${pos.x},${pos.y})`).join(';');
    const wordIds = group.wordIds.join(';');
    
    rows.push([
      `"${group.wordText}"`,
      group.usageCount.toString(),
      `"${positions}"`,
      `"${wordIds}"`
    ].join(','));
  });

  return rows.join('\n');
}

/**
 * 导出重复检测报告为JSON格式
 */
export function exportDuplicateReportToJSON(report: DuplicateReport): string {
  return JSON.stringify(report, null, 2);
}

/**
 * 比较两次重复检测结果
 */
export function compareDuplicateDetections(
  before: DuplicateDetection,
  after: DuplicateDetection
): {
  added: string[];
  removed: string[];
  changed: Array<{ wordText: string; beforeCount: number; afterCount: number }>;
  summary: {
    duplicateChange: number;
    rateChange: number;
  };
} {
  const beforeWords = new Set(before.duplicateWords);
  const afterWords = new Set(after.duplicateWords);

  const added = Array.from(afterWords).filter(word => !beforeWords.has(word));
  const removed = Array.from(beforeWords).filter(word => !afterWords.has(word));

  const changed: Array<{ wordText: string; beforeCount: number; afterCount: number }> = [];
  
  beforeWords.forEach(wordText => {
    if (afterWords.has(wordText)) {
      const beforeGroup = before.duplicateGroups.get(wordText);
      const afterGroup = after.duplicateGroups.get(wordText);
      
      if (beforeGroup && afterGroup && beforeGroup.usageCount !== afterGroup.usageCount) {
        changed.push({
          wordText,
          beforeCount: beforeGroup.usageCount,
          afterCount: afterGroup.usageCount,
        });
      }
    }
  });

  return {
    added,
    removed,
    changed,
    summary: {
      duplicateChange: after.statistics.totalDuplicates - before.statistics.totalDuplicates,
      rateChange: after.statistics.duplicateRate - before.statistics.duplicateRate,
    },
  };
}

// ===== 辅助函数 =====

/**
 * 在词库中查找词语
 */
function findWordById(wordLibrary: WordLibraryData, wordId: string): WordEntry | undefined {
  for (const colorCategories of Object.values(wordLibrary.categories)) {
    for (const levelWords of Object.values(colorCategories)) {
      const word = levelWords.find(w => w.id === wordId);
      if (word) return word;
    }
  }
  return undefined;
}
