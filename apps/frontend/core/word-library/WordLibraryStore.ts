/**
 * 词库状态管理
 * 🎯 核心价值：统一的词库状态管理，支持词语管理、绑定关系、版本控制
 * 📦 功能范围：词库数据、单元格绑定、导航状态、重复检测、版本管理
 * 🔄 架构设计：基于Zustand的响应式状态管理，支持持久化和计算属性缓存
 */

import { enableMapSet, produce } from 'immer';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { BasicColorType } from '../matrix/MatrixTypes';
import { DuplicateDetectionService } from './DuplicateDetectionService';
import { VersionManagementService } from './VersionManagementService';
import type {
  CellKey,
  CellWordBinding,
  CellWordBindings,
  ColorLevel,
  CreateWordParams,
  DuplicateDetection,
  FilterCriteria,
  NavigationState,
  UpdateWordParams,
  WordEntry,
  WordId,
  WordLibraryData,
  WordVersion
} from './WordLibraryTypes';
import {
  createDefaultDuplicateDetection,
  createDefaultNavigationState,
  createDefaultWordMetadata,
  createDefaultWordValidation,
  generateCellKey,
  generateWordId,
  validateChineseText
} from './WordLibraryTypes';

// 启用 Immer 的 MapSet 插件以支持 Map 和 Set 数据结构
enableMapSet();

// ===== 状态接口 =====

interface WordLibraryStoreState {
  // === 核心数据 ===
  wordLibrary: WordLibraryData;
  cellWordBindings: CellWordBindings;
  wordVersions: WordVersion[];
  currentVersionId: string | null;

  // === 导航和交互状态 ===
  navigationState: NavigationState;
  duplicateDetection: DuplicateDetection;

  // === 状态标识 ===
  isLoading: boolean;
  isDirty: boolean;
  lastUpdate: number;
}

interface WordLibraryStoreActions {
  // === 词库管理操作 ===
  addWord: (word: CreateWordParams) => Promise<WordId>;
  updateWord: (wordId: WordId, updates: UpdateWordParams) => Promise<void>;
  removeWord: (wordId: WordId) => Promise<void>;
  validateWord: (text: string) => boolean;
  getWordsByCategory: (color: BasicColorType, level: ColorLevel) => WordEntry[];
  getAllWords: () => WordEntry[];
  searchWords: (searchText: string) => WordEntry[];

  // === 单元格绑定操作 ===
  bindWordToCell: (x: number, y: number, wordId: WordId) => Promise<void>;
  unbindWordFromCell: (x: number, y: number) => Promise<void>;
  previewWordInCell: (x: number, y: number, wordId: WordId) => void;
  clearPreview: () => void;
  getCellBinding: (x: number, y: number) => CellWordBinding | null;

  // === 导航操作 ===
  activateWordSelection: (x: number, y: number) => void;
  navigateWords: (direction: 'left' | 'right' | 'up' | 'down') => void;
  confirmSelection: () => Promise<void>;
  cancelSelection: () => void;
  setFilterCriteria: (criteria: FilterCriteria) => void;

  // === 版本管理操作 ===
  saveWordVersion: (name: string, description?: string) => Promise<string>;
  loadWordVersion: (versionId: string) => Promise<void>;
  deleteWordVersion: (versionId: string) => Promise<void>;
  compareVersions: (fromVersionId: string, toVersionId: string) => Promise<any>;
  exportVersion: (versionId: string) => Promise<string>;
  importVersion: (jsonData: string) => Promise<string>;
  enableAutoSave: () => void;
  disableAutoSave: () => void;

  // === 重复检测操作 ===
  detectDuplicates: (forceFullDetection?: boolean) => void;
  getDuplicateWords: () => string[];
  getDuplicateGroups: () => Map<string, any>;
  getDuplicateStatistics: () => any;
  clearDuplicateCache: () => void;
  getDuplicateCacheStats: () => any;

  // === 数据同步操作 ===
  syncWithServer: () => Promise<void>;
  exportWordLibrary: () => string;
  importWordLibrary: (data: string) => Promise<void>;

  // === 初始化和清理 ===
  initializeWordLibrary: () => void;
  clearWordLibrary: () => void;
}

export type WordLibraryStore = WordLibraryStoreState & WordLibraryStoreActions;

// ===== 服务实例 =====

/** 重复检测服务实例 */
const duplicateDetectionService = new DuplicateDetectionService({
  enableRealTimeDetection: true,
  enableCache: true,
  batchDetectionThreshold: 500,
  cacheExpirationTime: 5 * 60 * 1000, // 5分钟
  ignoreTemporaryBindings: true,
});

/** 版本管理服务实例 */
const versionManagementService = new VersionManagementService({
  maxVersions: 50,
  enableAutoSave: false,
  autoSaveInterval: 5 * 60 * 1000, // 5分钟
  enableCompression: true,
  namingPattern: 'timestamp',
});

// ===== 初始状态 =====

const createInitialWordLibrary = (): WordLibraryData => ({
  categories: {
    red: { 1: [], 2: [], 3: [], 4: [] },
    cyan: { 1: [], 2: [], 3: [], 4: [] },
    yellow: { 1: [], 2: [], 3: [], 4: [] },
    purple: { 1: [], 2: [], 3: [], 4: [] },
    orange: { 1: [], 2: [], 3: [], 4: [] },
    green: { 1: [], 2: [], 3: [], 4: [] },
    blue: { 1: [], 2: [], 3: [], 4: [] },
    pink: { 1: [], 2: [], 3: [], 4: [] },
    black: { 1: [], 2: [], 3: [], 4: [] },
  },
  metadata: {
    totalWords: 0,
    lastUpdated: new Date(),
    version: '1.0.0',
  },
  statistics: {
    wordsByColor: {
      red: 0, cyan: 0, yellow: 0, purple: 0, orange: 0,
      green: 0, blue: 0, pink: 0, black: 0,
    },
    wordsByLevel: { 1: 0, 2: 0, 3: 0, 4: 0 },
    duplicateWords: [],
  },
});

// ===== 工具函数 =====

const updateWordLibraryStatistics = (wordLibrary: WordLibraryData): void => {
  let totalWords = 0;
  const wordsByColor: Record<BasicColorType, number> = {
    red: 0, cyan: 0, yellow: 0, purple: 0, orange: 0,
    green: 0, blue: 0, pink: 0, black: 0,
  };
  const wordsByLevel: Record<ColorLevel, number> = { 1: 0, 2: 0, 3: 0, 4: 0 };

  Object.entries(wordLibrary.categories).forEach(([color, levels]) => {
    Object.entries(levels).forEach(([level, words]) => {
      const colorKey = color as BasicColorType;
      const levelKey = parseInt(level) as ColorLevel;
      const wordCount = words.length;

      totalWords += wordCount;
      wordsByColor[colorKey] += wordCount;
      wordsByLevel[levelKey] += wordCount;
    });
  });

  wordLibrary.metadata.totalWords = totalWords;
  wordLibrary.metadata.lastUpdated = new Date();
  wordLibrary.statistics.wordsByColor = wordsByColor;
  wordLibrary.statistics.wordsByLevel = wordsByLevel;
};

const findWordInLibrary = (wordLibrary: WordLibraryData, wordId: WordId): WordEntry | null => {
  for (const colorCategories of Object.values(wordLibrary.categories)) {
    for (const levelWords of Object.values(colorCategories)) {
      const word = levelWords.find(w => w.id === wordId);
      if (word) return word;
    }
  }
  return null;
};

const removeWordFromLibrary = (wordLibrary: WordLibraryData, wordId: WordId): boolean => {
  for (const colorCategories of Object.values(wordLibrary.categories)) {
    for (const levelWords of Object.values(colorCategories)) {
      const index = levelWords.findIndex(w => w.id === wordId);
      if (index !== -1) {
        levelWords.splice(index, 1);
        return true;
      }
    }
  }
  return false;
};

// ===== Store实现 =====

export const useWordLibraryStore = create<WordLibraryStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      wordLibrary: createInitialWordLibrary(),
      cellWordBindings: new Map(),
      wordVersions: [],
      currentVersionId: null,
      navigationState: createDefaultNavigationState(),
      duplicateDetection: createDefaultDuplicateDetection(),
      isLoading: false,
      isDirty: false,
      lastUpdate: Date.now(),

      // === 词库管理操作 ===
      addWord: async (word: CreateWordParams): Promise<WordId> => {
        return new Promise((resolve, reject) => {
          try {
            const wordId = generateWordId();
            const newWord: WordEntry = {
              id: wordId,
              text: word.text,
              category: word.category,
              metadata: createDefaultWordMetadata(),
              validation: createDefaultWordValidation(word.text),
            };

            if (!newWord.validation.isValid) {
              reject(new Error('词语验证失败'));
              return;
            }

            set(produce((state) => {
              const { color, level } = word.category;
              state.wordLibrary.categories[color][level].push(newWord);
              updateWordLibraryStatistics(state.wordLibrary);
              state.isDirty = true;
              state.lastUpdate = Date.now();
            }));

            resolve(wordId);
          } catch (error) {
            reject(error);
          }
        });
      },

      updateWord: async (wordId: WordId, updates: UpdateWordParams): Promise<void> => {
        return new Promise((resolve, reject) => {
          try {
            set(produce((state) => {
              const word = findWordInLibrary(state.wordLibrary, wordId);
              if (!word) {
                throw new Error('词语不存在');
              }

              // 如果更新了文本，重新验证
              if (updates.text) {
                const validation = createDefaultWordValidation(updates.text);
                if (!validation.isValid) {
                  throw new Error('词语验证失败');
                }
                word.validation = validation;
              }

              // 应用更新
              Object.assign(word, updates);
              word.metadata.updatedAt = new Date();

              updateWordLibraryStatistics(state.wordLibrary);
              state.isDirty = true;
              state.lastUpdate = Date.now();
            }));

            resolve();
          } catch (error) {
            reject(error);
          }
        });
      },

      removeWord: async (wordId: WordId): Promise<void> => {
        return new Promise((resolve, reject) => {
          try {
            set(produce((state) => {
              const removed = removeWordFromLibrary(state.wordLibrary, wordId);
              if (!removed) {
                throw new Error('词语不存在');
              }

              // 移除相关的绑定关系
              const bindingsToRemove: CellKey[] = [];
              state.cellWordBindings.forEach((binding: any, cellKey: any) => {
                if (binding.wordId === wordId) {
                  bindingsToRemove.push(cellKey);
                }
              });

              bindingsToRemove.forEach(cellKey => {
                state.cellWordBindings.delete(cellKey);
              });

              updateWordLibraryStatistics(state.wordLibrary);
              state.isDirty = true;
              state.lastUpdate = Date.now();
            }));

            resolve();
          } catch (error) {
            reject(error);
          }
        });
      },

      validateWord: (text: string): boolean => {
        return validateChineseText(text);
      },

      getWordsByCategory: (color: BasicColorType, level: ColorLevel): WordEntry[] => {
        const state = get();
        return state.wordLibrary.categories[color][level] || [];
      },

      getAllWords: (): WordEntry[] => {
        const state = get();
        const allWords: WordEntry[] = [];

        // 遍历所有颜色和等级
        Object.keys(state.wordLibrary.categories).forEach(color => {
          Object.keys(state.wordLibrary.categories[color as BasicColorType]).forEach(level => {
            const words = state.wordLibrary.categories[color as BasicColorType][parseInt(level) as ColorLevel];
            if (words) {
              allWords.push(...words);
            }
          });
        });

        return allWords;
      },

      searchWords: (searchText: string): WordEntry[] => {
        const state = get();
        const results: WordEntry[] = [];

        Object.values(state.wordLibrary.categories).forEach(colorCategories => {
          Object.values(colorCategories).forEach(levelWords => {
            levelWords.forEach(word => {
              if (word.text.includes(searchText)) {
                results.push(word);
              }
            });
          });
        });

        return results;
      },

      // === 单元格绑定操作 ===
      bindWordToCell: async (x: number, y: number, wordId: WordId): Promise<void> => {
        return new Promise((resolve, reject) => {
          try {
            const cellKey = generateCellKey(x, y);
            const word = findWordInLibrary(get().wordLibrary, wordId);

            if (!word) {
              reject(new Error('词语不存在'));
              return;
            }

            set(produce((state) => {
              const binding: CellWordBinding = {
                cellKey,
                wordId,
                bindingTime: new Date(),
                isTemporary: false,
              };

              state.cellWordBindings.set(cellKey, binding);

              // 更新词语使用统计
              word.metadata.usageCount++;
              word.metadata.lastUsed = new Date();

              state.isDirty = true;
              state.lastUpdate = Date.now();
            }));

            // 触发重复检测
            get().detectDuplicates();
            resolve();
          } catch (error) {
            reject(error);
          }
        });
      },

      unbindWordFromCell: async (x: number, y: number): Promise<void> => {
        return new Promise((resolve) => {
          const cellKey = generateCellKey(x, y);

          set(produce((state) => {
            state.cellWordBindings.delete(cellKey);
            state.isDirty = true;
            state.lastUpdate = Date.now();
          }));

          // 触发重复检测
          get().detectDuplicates();
          resolve();
        });
      },

      previewWordInCell: (x: number, y: number, wordId: WordId): void => {
        const cellKey = generateCellKey(x, y);

        set(produce((state) => {
          const binding: CellWordBinding = {
            cellKey,
            wordId,
            bindingTime: new Date(),
            isTemporary: true,
          };

          state.cellWordBindings.set(cellKey, binding);
          state.navigationState.previewMode = true;
        }));
      },

      clearPreview: (): void => {
        set(produce((state) => {
          // 移除所有临时绑定
          const keysToRemove: CellKey[] = [];
          state.cellWordBindings.forEach((binding: any, cellKey: any) => {
            if (binding.isTemporary) {
              keysToRemove.push(cellKey);
            }
          });

          keysToRemove.forEach(cellKey => {
            state.cellWordBindings.delete(cellKey);
          });

          state.navigationState.previewMode = false;
        }));
      },

      getCellBinding: (x: number, y: number): CellWordBinding | null => {
        const cellKey = generateCellKey(x, y);
        return get().cellWordBindings.get(cellKey) || null;
      },

      // === 导航操作 ===
      activateWordSelection: (x: number, y: number): void => {
        set(produce((state) => {
          state.navigationState.isActive = true;
          state.navigationState.targetCell = { x, y };
          state.navigationState.selectedIndex = 0;
          state.navigationState.previewMode = false;

          // TODO: 根据单元格颜色和等级过滤可用词语
          // 这里需要与MatrixStore集成来获取单元格信息
        }));
      },

      navigateWords: (direction: 'left' | 'right' | 'up' | 'down'): void => {
        set(produce((state) => {
          if (!state.navigationState.isActive) return;

          const { availableWords, selectedIndex } = state.navigationState;

          if (direction === 'left' && selectedIndex > 0) {
            state.navigationState.selectedIndex = selectedIndex - 1;
          } else if (direction === 'right' && selectedIndex < availableWords.length - 1) {
            state.navigationState.selectedIndex = selectedIndex + 1;
          }
          // up/down 可以用于预览/清除预览
        }));
      },

      confirmSelection: async (): Promise<void> => {
        const state = get();
        const { navigationState } = state;

        if (!navigationState.isActive || !navigationState.targetCell) {
          return;
        }

        const selectedWord = navigationState.availableWords[navigationState.selectedIndex];
        if (selectedWord) {
          await get().bindWordToCell(
            navigationState.targetCell.x,
            navigationState.targetCell.y,
            selectedWord.id
          );
        }

        get().cancelSelection();
      },

      cancelSelection: (): void => {
        set(produce((state) => {
          state.navigationState.isActive = false;
          state.navigationState.targetCell = null;
          state.navigationState.availableWords = [];
          state.navigationState.selectedIndex = 0;
          state.navigationState.previewMode = false;
        }));

        get().clearPreview();
      },

      setFilterCriteria: (criteria: FilterCriteria): void => {
        set(produce((state) => {
          state.navigationState.filterCriteria = criteria;
          // TODO: 重新过滤可用词语
        }));
      },

      // === 重复检测操作 ===
      detectDuplicates: (forceFullDetection = false): void => {
        const state = get();
        const result = duplicateDetectionService.detectDuplicates(
          state.cellWordBindings,
          state.wordLibrary,
          forceFullDetection
        );

        set(produce((draft) => {
          draft.duplicateDetection = result;
        }));
      },

      getDuplicateWords: (): string[] => {
        return Array.from(get().duplicateDetection.duplicateWords);
      },

      getDuplicateGroups: (): Map<string, any> => {
        return get().duplicateDetection.duplicateGroups;
      },

      getDuplicateStatistics: () => {
        return get().duplicateDetection.statistics;
      },

      clearDuplicateCache: (): void => {
        duplicateDetectionService.clearCache();
      },

      getDuplicateCacheStats: () => {
        return duplicateDetectionService.getCacheStats();
      },

      // === 版本管理操作 ===
      saveWordVersion: async (name: string, description?: string): Promise<string> => {
        const state = get();

        try {
          const snapshot = versionManagementService.createVersionSnapshot(
            name,
            description || '',
            state.wordLibrary,
            state.cellWordBindings,
            'user'
          );

          await versionManagementService.saveVersion(snapshot, 'localStorage');

          set(produce((draft) => {
            draft.wordVersions.push(snapshot.version);
            draft.currentVersionId = snapshot.version.id;
            draft.isDirty = false;
            draft.lastUpdate = Date.now();
          }));

          return snapshot.version.id;
        } catch (error) {
          throw new Error(`保存版本失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
      },

      loadWordVersion: async (versionId: string): Promise<void> => {
        try {
          const snapshot = await versionManagementService.loadVersion(versionId, 'localStorage');

          set(produce((draft) => {
            // 清除当前绑定
            draft.cellWordBindings.clear();

            // 加载版本绑定
            snapshot.bindings.forEach(binding => {
              draft.cellWordBindings.set(binding.cellKey, { ...binding });
            });

            // 恢复词库数据
            draft.wordLibrary = snapshot.wordLibrary;
            draft.currentVersionId = versionId;
            draft.isDirty = false;
            draft.lastUpdate = Date.now();
          }));

          // 触发重复检测
          get().detectDuplicates();
        } catch (error) {
          throw new Error(`加载版本失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
      },

      deleteWordVersion: async (versionId: string): Promise<void> => {
        return new Promise((resolve) => {
          set(produce((state) => {
            const index = state.wordVersions.findIndex((v: any) => v.id === versionId);
            if (index !== -1) {
              state.wordVersions.splice(index, 1);
              if (state.currentVersionId === versionId) {
                state.currentVersionId = null;
              }
              state.isDirty = true;
              state.lastUpdate = Date.now();
            }
          }));

          resolve();
        });
      },

      // === 数据同步操作 ===
      syncWithServer: async (): Promise<void> => {
        // TODO: 实现服务器同步逻辑
        console.log('同步功能待实现');
      },

      exportWordLibrary: (): string => {
        const state = get();
        return JSON.stringify({
          wordLibrary: state.wordLibrary,
          cellWordBindings: Array.from(state.cellWordBindings.entries()),
          wordVersions: state.wordVersions,
          currentVersionId: state.currentVersionId,
        });
      },

      importWordLibrary: async (data: string): Promise<void> => {
        return new Promise((resolve, reject) => {
          try {
            const imported = JSON.parse(data);

            set(produce((state) => {
              state.wordLibrary = imported.wordLibrary;
              state.cellWordBindings = new Map(imported.cellWordBindings);
              state.wordVersions = imported.wordVersions;
              state.currentVersionId = imported.currentVersionId;
              state.isDirty = true;
              state.lastUpdate = Date.now();
            }));

            get().detectDuplicates();
            resolve();
          } catch (error) {
            reject(error);
          }
        });
      },

      compareVersions: async (fromVersionId: string, toVersionId: string): Promise<any> => {
        try {
          const fromSnapshot = await versionManagementService.loadVersion(fromVersionId, 'localStorage');
          const toSnapshot = await versionManagementService.loadVersion(toVersionId, 'localStorage');
          return versionManagementService.compareVersions(fromSnapshot, toSnapshot);
        } catch (error) {
          throw new Error(`版本比较失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
      },

      exportVersion: async (versionId: string): Promise<string> => {
        try {
          const snapshot = await versionManagementService.loadVersion(versionId, 'localStorage');
          return versionManagementService.exportVersionToJSON(snapshot);
        } catch (error) {
          throw new Error(`版本导出失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
      },

      importVersion: async (jsonData: string): Promise<string> => {
        try {
          const snapshot = versionManagementService.importVersionFromJSON(jsonData);
          await versionManagementService.saveVersion(snapshot, 'localStorage');

          set(produce((draft) => {
            draft.wordVersions.push(snapshot.version);
          }));

          return snapshot.version.id;
        } catch (error) {
          throw new Error(`版本导入失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
      },

      enableAutoSave: (): void => {
        versionManagementService.enableAutoSave(async () => {
          const state = get();
          if (state.isDirty) {
            await get().saveWordVersion('自动保存', '系统自动保存的版本');
          }
        });
      },

      disableAutoSave: (): void => {
        versionManagementService.disableAutoSave();
      },

      // === 初始化和清理 ===
      initializeWordLibrary: (): void => {
        set(produce((state) => {
          state.wordLibrary = createInitialWordLibrary();
          state.cellWordBindings.clear();
          state.wordVersions = [];
          state.currentVersionId = null;
          state.navigationState = createDefaultNavigationState();
          state.duplicateDetection = createDefaultDuplicateDetection();
          state.isDirty = false;
          state.lastUpdate = Date.now();
        }));
      },

      clearWordLibrary: (): void => {
        set(produce((state) => {
          state.cellWordBindings.clear();
          state.navigationState = createDefaultNavigationState();
          state.duplicateDetection = createDefaultDuplicateDetection();
          state.isDirty = true;
          state.lastUpdate = Date.now();
        }));
      },
    }),
    {
      name: 'word-library-store',
      version: 1,
      // 部分持久化，排除临时状态
      partialize: (state) => ({
        wordLibrary: state.wordLibrary,
        cellWordBindings: state.cellWordBindings,
        wordVersions: state.wordVersions,
        currentVersionId: state.currentVersionId,
      }),
    }
  )
);

// ===== 选择器钩子 =====

export const useWordLibrary = () => useWordLibraryStore((state) => state.wordLibrary);
export const useCellWordBindings = () => useWordLibraryStore((state) => state.cellWordBindings);
export const useWordVersions = () => useWordLibraryStore((state) => state.wordVersions);
export const useCurrentVersionId = () => useWordLibraryStore((state) => state.currentVersionId);
export const useNavigationState = () => useWordLibraryStore((state) => state.navigationState);
export const useDuplicateDetection = () => useWordLibraryStore((state) => state.duplicateDetection);
export const useIsWordLibraryDirty = () => useWordLibraryStore((state) => state.isDirty);
